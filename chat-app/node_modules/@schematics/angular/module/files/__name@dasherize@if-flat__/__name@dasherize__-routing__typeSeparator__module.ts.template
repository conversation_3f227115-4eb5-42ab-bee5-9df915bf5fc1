import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';<% if (lazyRoute) { %>
import { <%= classify(name) %> } from './<%= dasherize(name) %>';<% } %>

const routes: Routes = [<% if (lazyRoute) { %>{ path: '', component: <%= classify(name) %> }<% } %>];

@NgModule({
  imports: [RouterModule.for<%= routingScope %>(routes)],
  exports: [RouterModule]
})
export class <%= classify(name) %>RoutingModule { }
