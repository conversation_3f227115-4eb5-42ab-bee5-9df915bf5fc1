{"version": 3, "file": "kk.js", "sourceRoot": "", "sources": ["kk.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,0BAA0B,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,eAAe,EAAC,eAAe,EAAC,qBAAqB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,UAAU,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"kk\",[[\"AM\",\"PM\"],u,u],u,[[\"Ж\",\"Д\",\"С\",\"С\",\"Б\",\"Ж\",\"С\"],[\"жс\",\"дс\",\"сс\",\"ср\",\"бс\",\"жм\",\"сб\"],[\"жексенбі\",\"дүйсенбі\",\"сейсенбі\",\"сәрсенбі\",\"бейсенбі\",\"жұма\",\"сенбі\"],[\"жс\",\"дс\",\"сс\",\"ср\",\"бс\",\"жм\",\"сб\"]],u,[[\"Қ\",\"А\",\"Н\",\"С\",\"М\",\"М\",\"Ш\",\"Т\",\"Қ\",\"Қ\",\"Қ\",\"Ж\"],[\"қаң.\",\"ақп.\",\"нау.\",\"сәу.\",\"мам.\",\"мау.\",\"шіл.\",\"там.\",\"қыр.\",\"қаз.\",\"қар.\",\"жел.\"],[\"қаңтар\",\"ақпан\",\"наурыз\",\"сәуір\",\"мамыр\",\"маусым\",\"шілде\",\"тамыз\",\"қыркүйек\",\"қазан\",\"қараша\",\"желтоқсан\"]],[[\"Қ\",\"А\",\"Н\",\"С\",\"М\",\"М\",\"Ш\",\"Т\",\"Қ\",\"Қ\",\"Қ\",\"Ж\"],[\"қаң.\",\"ақп.\",\"нау.\",\"сәу.\",\"мам.\",\"мау.\",\"шіл.\",\"там.\",\"қыр.\",\"қаз.\",\"қар.\",\"жел.\"],[\"Қаңтар\",\"Ақпан\",\"Наурыз\",\"Сәуір\",\"Мамыр\",\"Маусым\",\"Шілде\",\"Тамыз\",\"Қыркүйек\",\"Қазан\",\"Қараша\",\"Желтоқсан\"]],[[\"б.з.д.\",\"б.з.\"],u,[\"Біздің заманымызға дейін\",\"біздің заманымыз\"]],1,[6,0],[\"dd.MM.yy\",\"y 'ж'. dd MMM\",\"y 'ж'. d MMMM\",\"y 'ж'. d MMMM, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"сан емес\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"KZT\",\"₸\",\"Қазақстан теңгесі\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"KZT\":[\"₸\"],\"LSL\":[\"ЛСЛ\"],\"PHP\":[u,\"₱\"],\"RUB\":[\"₽\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}