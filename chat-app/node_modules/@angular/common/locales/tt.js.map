{"version": 3, "file": "tt.js", "sourceRoot": "", "sources": ["tt.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,YAAY,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,eAAe,EAAC,gBAAgB,EAAC,sBAAsB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"tt\",[[\"AM\",\"PM\"],u,u],u,[[\"Я\",\"Д\",\"С\",\"Ч\",\"П\",\"Җ\",\"Ш\"],[\"якш.\",\"дүш.\",\"сиш.\",\"чәр.\",\"пәнҗ.\",\"җом.\",\"шим.\"],[\"якшәмбе\",\"дүшәмбе\",\"сишәмбе\",\"чәршәмбе\",\"пәнҗешәмбе\",\"җомга\",\"шимбә\"],[\"якш.\",\"дүш.\",\"сиш.\",\"чәр.\",\"пәнҗ.\",\"җом.\",\"шим.\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"гыйн.\",\"фев.\",\"мар.\",\"апр.\",\"май\",\"июнь\",\"июль\",\"авг.\",\"сент.\",\"окт.\",\"нояб.\",\"дек.\"],[\"гыйнвар\",\"февраль\",\"март\",\"апрель\",\"май\",\"июнь\",\"июль\",\"август\",\"сентябрь\",\"октябрь\",\"ноябрь\",\"декабрь\"]],u,[[\"б.э.к.\",\"милади\"],u,[\"безнең эрага кадәр\",\"милади\"]],1,[6,0],[\"dd.MM.y\",\"d MMM, y 'ел'\",\"d MMMM, y 'ел'\",\"d MMMM, y 'ел', EEEE\"],[\"H:mm\",\"H:mm:ss\",\"H:mm:ss z\",\"H:mm:ss zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"RUB\",\"₽\",\"Россия сумы\",{\"JPY\":[\"JP¥\",\"¥\"],\"RUB\":[\"₽\"]},\"ltr\", plural];\n"]}