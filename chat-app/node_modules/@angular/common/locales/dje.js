/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["dje", [["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> b"], u, u], u, [["H", "T", "T", "L", "M", "Z", "S"], ["Alh", "Ati", "Ata", "Ala", "Alm", "<PERSON>z", "<PERSON>i"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], ["<PERSON>h", "Ati", "Ata", "Ala", "Alm", "<PERSON><PERSON>", "<PERSON>i"]], u, [["<PERSON>", "F", "M", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>u<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Okt", "<PERSON>o", "<PERSON>"], ["Žanwiye", "<PERSON>ewiriye", "<PERSON>i", "Awiril", "Me", "Žuweŋ", "Žuyye", "Ut", "Sektanbur", "Oktoobur", "Noowanbur", "<PERSON>sanbur"]], u, [["I<PERSON>", "I<PERSON>"], u, ["<PERSON>a jine", "<PERSON>a zamanoo"]], 1, [6, 0], ["d/M/y", "d MMM, y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "XOF", "F CFA", "CFA Fraŋ (BCEAO)", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=dje.js.map