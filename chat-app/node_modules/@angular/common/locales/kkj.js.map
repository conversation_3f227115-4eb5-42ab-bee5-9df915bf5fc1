{"version": 3, "file": "kkj.js", "sourceRoot": "", "sources": ["kkj.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,MAAM,EAAC,WAAW,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,MAAM,EAAC,WAAW,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,gBAAgB,EAAC,aAAa,EAAC,cAAc,EAAC,iBAAiB,EAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,WAAW,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"kkj\",[[\"AM\",\"PM\"],u,u],u,[[\"so\",\"lu\",\"ma\",\"mɛ\",\"ye\",\"va\",\"ms\"],[\"sɔndi\",\"lundi\",\"mardi\",\"mɛrkɛrɛdi\",\"yedi\",\"vaŋdɛrɛdi\",\"mɔnɔ sɔndi\"],u,u],[[\"so\",\"lu\",\"ma\",\"mɛ\",\"ye\",\"va\",\"ms\"],[\"sɔndi\",\"lundi\",\"mardi\",\"mɛrkɛrɛdi\",\"yedi\",\"vaŋdɛrɛdi\",\"mɔnɔ sɔndi\"],u,[\"so\",\"lu\",\"ma\",\"mɛ\",\"ye\",\"va\",\"ms\"]],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"pamba\",\"wanja\",\"mbiyɔ mɛndoŋgɔ\",\"Nyɔlɔmbɔŋgɔ\",\"Mɔnɔ ŋgbanja\",\"Nyaŋgwɛ ŋgbanja\",\"kuŋgwɛ\",\"fɛ\",\"njapi\",\"nyukul\",\"M11\",\"ɓulɓusɛ\"],u],u,[[\"BCE\",\"CE\"],u,u],1,[6,0],[\"dd/MM y\",\"d MMM y\",\"d MMMM y\",\"EEEE dd MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"XAF\",\"FCFA\",\"Franc CFA\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}