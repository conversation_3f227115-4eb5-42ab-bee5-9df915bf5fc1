/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === Math.floor(n) && (n >= 0 && n <= 1))
        return 1;
    return 5;
}
export default ["mg", [["AM", "PM"], u, u], u, [["A", "A", "T", "A", "A", "Z", "A"], ["Alah", "Alats", "<PERSON>l", "<PERSON>ar", "<PERSON><PERSON>", "<PERSON>om", "<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "Alats<PERSON>in<PERSON>", "<PERSON>lat<PERSON>", "Alarobia", "Alakamisy", "Zoma", "Asabotsy"], ["<PERSON><PERSON>", "Alats", "<PERSON>l", "<PERSON>ar", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "J", "J", "A", "<PERSON>", "O", "N", "D"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>y", "<PERSON>", "<PERSON>l", "<PERSON><PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "Des"], ["<PERSON>oary", "<PERSON>roa<PERSON>", "<PERSON>sa", "<PERSON>y", "<PERSON>y", "<PERSON>a", "<PERSON><PERSON>", "<PERSON>ogosi<PERSON>", "Septambra", "<PERSON>to<PERSON>", "<PERSON><PERSON>ra", "<PERSON>am<PERSON>"]], u, [["BC", "<PERSON>"], u, ["<PERSON>oh<PERSON>’i JK", "Aorian’i JK"]], 1, [6, 0], ["y-MM-dd", "y MMM d", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "MGA", "Ar", "Ariary", { "JPY": ["JP¥", "¥"], "MGA": ["Ar"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=mg.js.map