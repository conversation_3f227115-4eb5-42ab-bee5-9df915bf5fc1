{"version": 3, "file": "uz-Latn.js", "sourceRoot": "", "sources": ["uz-Latn.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,SAAS,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,YAAY,EAAC,WAAW,EAAC,MAAM,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,UAAU,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"uz-Latn\",[[\"TO\",\"TK\"],u,u],u,[[\"Y\",\"D\",\"S\",\"C\",\"P\",\"J\",\"S\"],[\"Yak\",\"Dush\",\"<PERSON><PERSON>\",\"Chor\",\"Pay\",\"Jum\",\"Shan\"],[\"yakshanba\",\"dushanba\",\"seshanba\",\"chorshanba\",\"payshanba\",\"juma\",\"shanba\"],[\"Ya\",\"Du\",\"Se\",\"Ch\",\"Pa\",\"Ju\",\"Sh\"]],u,[[\"Y\",\"F\",\"M\",\"A\",\"M\",\"I\",\"I\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"yan\",\"fev\",\"mar\",\"apr\",\"may\",\"iyn\",\"iyl\",\"avg\",\"sen\",\"okt\",\"noy\",\"dek\"],[\"yanvar\",\"fevral\",\"mart\",\"aprel\",\"may\",\"iyun\",\"iyul\",\"avgust\",\"sentabr\",\"oktabr\",\"noyabr\",\"dekabr\"]],[[\"Y\",\"F\",\"M\",\"A\",\"M\",\"I\",\"I\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"Yan\",\"Fev\",\"Mar\",\"Apr\",\"May\",\"Iyn\",\"Iyl\",\"Avg\",\"Sen\",\"Okt\",\"Noy\",\"Dek\"],[\"Yanvar\",\"Fevral\",\"Mart\",\"Aprel\",\"May\",\"Iyun\",\"Iyul\",\"Avgust\",\"Sentabr\",\"Oktabr\",\"Noyabr\",\"Dekabr\"]],[[\"m.a.\",\"milodiy\"],u,[\"miloddan avvalgi\",\"milodiy\"]],1,[6,0],[\"dd/MM/yy\",\"d-MMM, y\",\"d-MMMM, y\",\"EEEE, d-MMMM, y\"],[\"HH:mm\",\"HH:mm:ss\",\"H:mm:ss (z)\",\"H:mm:ss (zzzz)\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"son emas\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"UZS\",\"soʻm\",\"O‘zbekiston so‘mi\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"USD\":[\"US$\",\"$\"],\"UZS\":[\"soʻm\"]},\"ltr\", plural];\n"]}