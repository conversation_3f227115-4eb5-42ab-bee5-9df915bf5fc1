{"version": 3, "file": "se.js", "sourceRoot": "", "sources": ["se.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,YAAY,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,CAAC,EAAC,CAAC,aAAa,EAAC,WAAW,EAAC,YAAY,EAAC,aAAa,EAAC,WAAW,EAAC,WAAW,EAAC,WAAW,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,eAAe,EAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,YAAY,EAAC,YAAY,EAAC,cAAc,EAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,MAAM,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nif (n === 2)\n    return 2;\nreturn 5;\n}\n\nexport default [\"se\",[[\"i.b.\",\"e.b.\"],u,[\"iđitbeaivet\",\"eahketbeaivet\"]],[[\"i.b.\",\"e.b.\"],u,[\"iđitbeaivi\",\"eahketbeaivi\"]],[[\"S\",\"V\",\"M\",\"G\",\"D\",\"B\",\"L\"],[\"sotn\",\"vuos\",\"maŋ\",\"gask\",\"duor\",\"bear\",\"láv\"],[\"sotnabeaivi\",\"vuossárga\",\"maŋŋeb<PERSON>rga\",\"gaskavahk<PERSON>\",\"duorasdat\",\"bearjadat\",\"lávvardat\"],[\"sotn\",\"vuos\",\"maŋ\",\"gask\",\"duor\",\"bear\",\"láv\"]],u,[[\"O\",\"<PERSON>\",\"N\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"S\",\"B\",\"<PERSON>\",\"<PERSON>\",\"S\",\"J\"],[\"ođđj\",\"guov\",\"njuk\",\"cuo\",\"mies\",\"geas\",\"suoi\",\"borg\",\"čakč\",\"golg\",\"sk<PERSON>b\",\"juov\"],[\"ođđajagemánnu\",\"guovvamánnu\",\"njukčamánnu\",\"cuoŋománnu\",\"miessemánnu\",\"geassemánnu\",\"suoidnemánnu\",\"borgemánnu\",\"čakčamánnu\",\"golggotmánnu\",\"skábmamánnu\",\"juovlamánnu\"]],u,[[\"o.Kr.\",\"m.Kr.\"],u,[\"ovdal Kristtusa\",\"maŋŋel Kristtusa\"]],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"−\",\"·10^\",\"·\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"NOK\",\"kr\",\"norgga kruvdno\",{\"DKK\":[\"Dkr\",\"kr\"],\"JPY\":[\"JP¥\",\"¥\"],\"NOK\":[\"kr\"],\"SEK\":[\"Skr\",\"kr\"],\"THB\":[\"฿\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}