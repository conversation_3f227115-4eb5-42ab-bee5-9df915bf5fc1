/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["kln", [["krn", "koosk"], u, ["karoon", "kooskoliny"]], [["krn", "koosk"], u, u], [["T", "T", "O", "S", "A", "M", "L"], ["Kts", "Kot", "Koo", "<PERSON><PERSON>", "Koa", "Kom", "Kol"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Koaeng’", "Kosomok", "Ko<PERSON>’wan", "<PERSON>muut", "Ko<PERSON>"], ["Kts", "Ko<PERSON>", "Ko<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "T", "I", "M", "P", "N", "R", "B", "E", "K", "K"], ["Mul", "Ngat", "Taa", "Iwo", "Mam", "<PERSON>a", "Nge", "Roo", "Bur", "<PERSON>pe", "Kpt", "Kpa"], ["Mulgul", "Ng’atyaato", "Kiptaamo", "Iwootkuut", "Mamuut", "Paagi", "Ng’eiyeet", "Rooptui", "Bureet", "Epeeso", "Kipsuunde ne taai", "Kipsuunde nebo aeng’"]], u, [["AM", "KO"], u, ["Amait kesich Jesu", "Kokakesich Jesu"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Silingitab ya Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=kln.js.map