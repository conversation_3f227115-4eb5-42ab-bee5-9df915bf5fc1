/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["wo", [["<PERSON>", "Ngo"], u, u], u, [["Dib", "Alt", "Tal", "<PERSON><PERSON>", "Alx", "Àjj", "<PERSON><PERSON>"], u, ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], ["Dib", "Alt", "Tal", "<PERSON><PERSON>", "Alx", "Àjj", "Ase"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>wr", "<PERSON><PERSON>", "<PERSON>w", "<PERSON>", "U<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON>wiyee", "<PERSON><PERSON>yee", "<PERSON>", "Awril", "<PERSON>e", "<PERSON>we", "Sulet", "Ut", "Sàttumbar", "Oktoobar", "Nowàmbar", "Desàmbar"]], u, [["J<PERSON>", "AD"], u, ["av. J<PERSON>", "AD"]], 1, [6, 0], ["dd-MM-y", "d MMM, y", "d MMMM, y", "EEEE, d MMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} - {0}", u, "{1} 'ci' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "XOF", "F CFA", "Franc CFA bu Afrik Sowwu-jant", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=wo.js.map