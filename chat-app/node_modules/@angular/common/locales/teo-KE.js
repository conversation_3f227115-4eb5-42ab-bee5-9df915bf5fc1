/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["teo-KE", [["Taparachu", "Ebongi"], u, u], u, [["J", "B", "A", "U", "U", "K", "S"], ["Jum", "Bar", "Aar", "Uni", "Ung", "Kan", "Sab"], ["Nakaejuma", "<PERSON>kaeb<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>’on", "Nakakany", "<PERSON><PERSON><PERSON><PERSON>"], ["Jum", "<PERSON>", "A<PERSON>", "Uni", "Ung", "Kan", "Sab"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "T", "<PERSON>", "<PERSON>"], ["Rar", "Mu<PERSON>", "<PERSON>wa", "Dun", "<PERSON>", "<PERSON>d", "<PERSON>l", "<PERSON>ed", "<PERSON>k", "<PERSON>ib", "Lab", "<PERSON>o"], ["<PERSON>ara", "<PERSON>muk", "<PERSON>wamg’", "<PERSON>dung’el", "<PERSON><PERSON>", "<PERSON>modok’king’ol", "<PERSON><PERSON>la", "<PERSON><PERSON>l", "<PERSON>so<PERSON><PERSON><PERSON>", "<PERSON>tibar", "<PERSON>labor", "<PERSON>oo"]], u, [["<PERSON><PERSON>", "BK"], u, ["Kabla ya Christo", "Baada ya Christo"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Ango’otol lok’ Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "UGX": ["USh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=teo-KE.js.map