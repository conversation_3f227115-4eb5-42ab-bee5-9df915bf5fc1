/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["luy", [["a.m.", "p.m."], u, u], u, [["S", "M", "T", "W", "T", "F", "S"], ["J2", "J3", "J4", "J5", "<PERSON>", "Ij", "J1"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> wa Kanne", "<PERSON><PERSON><PERSON> wa <PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["J2", "J3", "J4", "J5", "<PERSON>", "Ij", "J1"]], u, [["J", "F", "<PERSON>", "A", "<PERSON>", "J", "J", "A", "<PERSON>", "O", "N", "<PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>"], ["<PERSON>ua<PERSON>", "<PERSON>ru<PERSON>", "<PERSON><PERSON>", "Aprili", "<PERSON>", "<PERSON>i", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>emba", "<PERSON>toba", "<PERSON>emba", "<PERSON>em<PERSON>"]], u, [["<PERSON>", "<PERSON>"], u, ["I<PERSON>i ya <PERSON><PERSON> <PERSON>wa", "<PERSON><PERSON>ga <PERSON>vita <PERSON><PERSON>"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00;¤- #,##0.00", "#E0"], "KES", "Ksh", "Sirinji ya Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=luy.js.map