{"version": 3, "file": "kgp.js", "sourceRoot": "", "sources": ["kgp.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,aAAa,EAAC,eAAe,EAAC,eAAe,EAAC,mBAAmB,EAAC,gBAAgB,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,eAAe,EAAC,gBAAgB,EAAC,sBAAsB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"kgp\",[[\"AM\",\"PM\"],u,u],u,[[\"N.\",\"P.\",\"R.\",\"T.\",\"V.\",\"P.\",\"S.\"],[\"num.\",\"pir.\",\"rég.\",\"tẽg.\",\"vẽn.\",\"pén.\",\"sav.\"],[\"numĩggu\",\"pir-kurã-há\",\"régre-kurã-há\",\"tẽgtũ-kurã-há\",\"vẽnhkãgra-kurã-há\",\"pénkar-kurã-há\",\"savnu\"],[\"N.\",\"1kh.\",\"2kh.\",\"3kh.\",\"4kh.\",\"5kh.\",\"S.\"]],u,[[\"1K\",\"2K\",\"3K\",\"4K\",\"5K\",\"6K\",\"7K\",\"8K\",\"9K\",\"10K\",\"11K\",\"12K\"],[\"1Ky.\",\"2Ky.\",\"3Ky.\",\"4Ky.\",\"5Ky.\",\"6Ky.\",\"7Ky.\",\"8Ky.\",\"9Ky.\",\"10Ky.\",\"11Ky.\",\"12Ky.\"],[\"1-Kysã\",\"2-Kysã\",\"3-Kysã\",\"4-Kysã\",\"5-Kysã\",\"6-Kysã\",\"7-Kysã\",\"8-Kysã\",\"9-Kysã\",\"10-Kysã\",\"11-Kysã\",\"12-Kysã\"]],u,[[\"C.j.\",\"C.kk.\"],u,[\"Cristo jo\",\"Cristo kar kỹ\"]],0,[6,0],[\"dd/MM/y\",\"d 'ne' MMM, y\",\"d 'ne' MMMM, y\",\"EEEE, d 'ne' MMMM, y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"BRL\",\"R$\",\"Mrasir Rejar\",{\"AUD\":[\"AU$\",\"$\"],\"BYN\":[u,\"p.\"],\"FJD\":[\"FJC\",\"$\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"PTE\":[\"Vẽj.\"],\"RON\":[u,\"L\"],\"SYP\":[u,\"S£\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"XOF\":[\"CFA\"],\"ZMK\":[\"SMK\"]},\"ltr\", plural];\n"]}