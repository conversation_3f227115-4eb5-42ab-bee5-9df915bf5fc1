{"version": 3, "file": "mua.js", "sourceRoot": "", "sources": ["mua.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,UAAU,EAAC,cAAc,EAAC,WAAW,EAAC,YAAY,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,cAAc,EAAC,YAAY,EAAC,YAAY,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,cAAc,EAAC,UAAU,EAAC,YAAY,EAAC,YAAY,EAAC,aAAa,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,YAAY,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"mua\",[[\"comme\",\"lilli\"],u,u],u,[[\"Y\",\"L\",\"Z\",\"O\",\"A\",\"G\",\"E\"],[\"<PERSON>a\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Cka\",\"Cga\",\"<PERSON><PERSON>\"],[\"Com’yakke\",\"Comlaaɗii\",\"Comzyiiɗii\",\"Comkolle\",\"Comkaldǝɓlii\",\"Comgaisuu\",\"Comzyeɓsuu\"],[\"Cy<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"C<PERSON>\",\"Cka\",\"Cga\",\"Cze\"]],u,[[\"O\",\"A\",\"I\",\"F\",\"D\",\"B\",\"L\",\"M\",\"E\",\"U\",\"W\",\"Y\"],[\"FLO\",\"CLA\",\"CKI\",\"FMF\",\"MAD\",\"MBI\",\"MLI\",\"MAM\",\"FDE\",\"FMU\",\"FGW\",\"FYU\"],[\"Fĩi Loo\",\"Cokcwaklaŋne\",\"Cokcwaklii\",\"Fĩi Marfoo\",\"Madǝǝuutǝbijaŋ\",\"Mamǝŋgwãafahbii\",\"Mamǝŋgwãalii\",\"Madǝmbii\",\"Fĩi Dǝɓlii\",\"Fĩi Mundaŋ\",\"Fĩi Gwahlle\",\"Fĩi Yuru\"]],u,[[\"KK\",\"PK\"],u,[\"KǝPel Kristu\",\"Pel Kristu\"]],1,[6,0],[\"d/M/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"XAF\",\"FCFA\",\"solai BEAC\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}