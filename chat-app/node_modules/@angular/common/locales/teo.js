/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["teo", [["Taparachu", "Ebongi"], u, u], u, [["J", "B", "A", "U", "U", "K", "S"], ["Jum", "Bar", "Aar", "Uni", "Ung", "Kan", "Sab"], ["Naka<PERSON><PERSON>", "Nakaebarasa", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>’on", "Nakakan<PERSON>", "Na<PERSON><PERSON><PERSON>"], ["Jum", "<PERSON>", "A<PERSON>", "Uni", "Ung", "Kan", "Sab"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "T", "<PERSON>", "<PERSON>"], ["Rar", "Mu<PERSON>", "<PERSON><PERSON>", "Dun", "<PERSON>", "Mod", "<PERSON>l", "<PERSON>ed", "<PERSON>k", "<PERSON>ib", "<PERSON>", "<PERSON>o"], ["Orara", "<PERSON>muk", "<PERSON>wamg’", "Odung’el", "<PERSON>uk", "<PERSON>modok’king’ol", "<PERSON>jola", "<PERSON><PERSON>l", "<PERSON>so<PERSON><PERSON>ma", "<PERSON>tibar", "<PERSON>labor", "<PERSON>oo"]], u, [["<PERSON><PERSON>", "<PERSON><PERSON>"], u, ["Kabla ya Christo", "Baada ya Christo"]], 1, [0, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "UGX", "USh", "Ango’otol lok’ Uganda", { "JPY": ["JP¥", "¥"], "UGX": ["USh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=teo.js.map