/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sr-Latn", [["AM", "PM"], u, u], [["pre podne", "po podne"], ["AM", "PM"], u], [["n", "p", "u", "s", "č", "p", "s"], ["ned", "pon", "uto", "sre", "čet", "pet", "sub"], ["nedelja", "ponedeljak", "utorak", "sreda", "četvrtak", "petak", "subota"], ["ne", "po", "ut", "sr", "če", "pe", "su"]], u, [["j", "f", "m", "a", "m", "j", "j", "a", "s", "o", "n", "d"], ["jan", "feb", "mar", "apr", "maj", "jun", "jul", "avg", "sep", "okt", "nov", "dec"], ["januar", "februar", "mart", "april", "maj", "jun", "jul", "avgust", "septembar", "oktobar", "novembar", "decembar"]], u, [["p.n.e.", "n.e."], ["p. n. e.", "n. e."], ["pre nove ere", "nove ere"]], 1, [6, 0], ["d.M.yy.", "d. M. y.", "d. MMMM y.", "EEEE, d. MMMM y."], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "RSD", "RSD", "srpski dinar", { "AUD": [u, "$"], "BAM": ["KM"], "BYN": [u, "r."], "GEL": [u, "ლ"], "KRW": [u, "₩"], "NZD": [u, "$"], "PHP": [u, "₱"], "TWD": ["NT$"], "USD": ["US$", "$"], "VND": [u, "₫"] }, "ltr", plural];
//# sourceMappingURL=sr-Latn.js.map