{"version": 3, "file": "sr-Cyrl-BA.js", "sourceRoot": "", "sources": ["sr-Cyrl-BA.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAEjK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC;QACnF,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QACrN,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,YAAY,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,aAAa,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,aAAa,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,OAAO,CAAC,EAAC,CAAC,gBAAgB,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,YAAY,EAAC,kBAAkB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,0CAA0C,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n\nif (v === 0 && (i % 10 === 1 && !(i % 100 === 11)) || f % 10 === 1 && !(f % 100 === 11))\n    return 1;\nif (v === 0 && (i % 10 === Math.floor(i % 10) && (i % 10 >= 2 && i % 10 <= 4) && !(i % 100 >= 12 && i % 100 <= 14)) || f % 10 === Math.floor(f % 10) && (f % 10 >= 2 && f % 10 <= 4) && !(f % 100 >= 12 && f % 100 <= 14))\n    return 3;\nreturn 5;\n}\n\nexport default [\"sr-Cyrl-BA\",[[\"AM\",\"PM\"],[\"прије подне\",\"по подне\"],u],[[\"а\",\"p\"],[\"прије подне\",\"по подне\"],u],[[\"н\",\"п\",\"у\",\"с\",\"ч\",\"п\",\"с\"],[\"нед\",\"пон\",\"уто\",\"сре\",\"чет\",\"пет\",\"суб\"],[\"недјеља\",\"понедјељак\",\"уторак\",\"сриједа\",\"четвртак\",\"петак\",\"субота\"],[\"не\",\"по\",\"ут\",\"ср\",\"че\",\"пе\",\"су\"]],[[\"н\",\"п\",\"у\",\"с\",\"ч\",\"п\",\"с\"],[\"нед\",\"пон\",\"уто\",\"сри\",\"чет\",\"пет\",\"суб\"],[\"недјеља\",\"понедјељак\",\"уторак\",\"сриједа\",\"четвртак\",\"петак\",\"субота\"],[\"не\",\"по\",\"ут\",\"ср\",\"че\",\"пе\",\"су\"]],[[\"ј\",\"ф\",\"м\",\"а\",\"м\",\"ј\",\"ј\",\"а\",\"с\",\"о\",\"н\",\"д\"],[\"јан\",\"феб\",\"мар\",\"апр\",\"мај\",\"јун\",\"јул\",\"авг\",\"сеп\",\"окт\",\"нов\",\"дец\"],[\"јануар\",\"фебруар\",\"март\",\"април\",\"мај\",\"јун\",\"јул\",\"август\",\"септембар\",\"октобар\",\"новембар\",\"децембар\"]],u,[[\"п.н.е.\",\"н.е.\"],[\"п. н. е.\",\"н. е.\"],[\"прије нове ере\",\"нове ере\"]],1,[6,0],[\"d.M.yy.\",\"d. M. y.\",\"d. MMMM y.\",\"EEEE, d. MMMM y.\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"BAM\",\"КМ\",\"Босанскохерцеговачка конвертибилна марка\",{\"AUD\":[u,\"$\"],\"BAM\":[\"КМ\",\"KM\"],\"BYN\":[u,\"р.\"],\"GEL\":[u,\"ლ\"],\"KRW\":[u,\"₩\"],\"NZD\":[u,\"$\"],\"PHP\":[u,\"₱\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"VND\":[u,\"₫\"]},\"ltr\", plural];\n"]}