/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["qu-BO", [["a.m.", "p.m."], u, u], u, [["D", "L", "M", "X", "J", "V", "S"], ["Dom", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sab"], ["<PERSON>", "<PERSON><PERSON>", "Mart<PERSON>", "Miércoles", "Ju<PERSON>", "Viernes", "<PERSON><PERSON>bad<PERSON>"], ["Dom", "Lun", "Mar", "<PERSON><PERSON>", "<PERSON>e", "Vie", "Sab"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Ene", "Feb", "Mar", "Abr", "May", "Jun", "Jul", "Ago", "<PERSON>", "Oct", "Nov", "Dic"], ["Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Setiembre", "Octubre", "Noviembre", "Diciembre"]], u, [["a.d.", "dC"], ["a.d.", "d.C."], ["ñawpa cristu", "chanta cristu"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, "{0} {1}", "{1} {0}"], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "¤ #,##0.00", "#E0"], "BOB", "Bs", "Boliviano", { "BBD": ["BBG", "$"], "BMD": ["DBM", "$"], "BOB": ["Bs"], "BZD": ["DBZ", "$"], "CAD": ["$CA", "$"], "GHS": [u, "GHC"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["$US", "$"] }, "ltr", plural];
//# sourceMappingURL=qu-BO.js.map