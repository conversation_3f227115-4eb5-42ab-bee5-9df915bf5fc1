/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ebu", [["KI", "UT"], u, u], u, [["K", "N", "N", "N", "A", "M", "N"], ["<PERSON>ma", "Tat", "Ine", "<PERSON>", "Arm", "Maa", "NMM"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Njumata<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>hii"], ["<PERSON>ma", "Tat", "Ine", "Tan", "Arm", "Maa", "NMM"]], u, [["M", "K", "K", "K", "G", "G", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Kan", "G<PERSON>", "<PERSON><PERSON>", "Mug", "Knn", "<PERSON>", "<PERSON><PERSON>", "Imw", "Igi"], ["Mweri wa mbere", "Mweri wa kaĩri", "Mweri wa kathatũ", "Mweri wa kana", "Mweri wa gatano", "Mweri wa gatantatũ", "Mweri wa mũgwanja", "Mweri wa kanana", "Mweri wa kenda", "Mweri wa ikũmi", "Mweri wa ikũmi na ũmwe", "Mweri wa ikũmi na Kaĩrĩ"]], u, [["MK", "TK"], u, ["Mbere ya Kristo", "Thutha wa Kristo"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Shilingi ya Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=ebu.js.map