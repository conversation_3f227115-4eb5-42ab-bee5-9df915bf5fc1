{"version": 3, "file": "ku.js", "sourceRoot": "", "sources": ["ku.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ku\",[[\"BN\",\"PN\"],u,u],u,[[\"Y\",\"D\",\"S\",\"Ç\",\"P\",\"Î\",\"Ş\"],[\"yş\",\"dş\",\"sş\",\"çş\",\"pş\",\"în\",\"ş\"],[\"yekşem\",\"duşem\",\"sêşem\",\"çarşem\",\"pêncşem\",\"în\",\"şemî\"],[\"yş\",\"dş\",\"sş\",\"çş\",\"pş\",\"în\",\"ş\"]],u,[[\"R\",\"R\",\"A\",\"A\",\"G\",\"P\",\"T\",\"G\",\"R\",\"K\",\"S\",\"B\"],[\"rêb\",\"reş\",\"ada\",\"avr\",\"gul\",\"pûş\",\"tîr\",\"gel\",\"rez\",\"kew\",\"ser\",\"ber\"],[\"rêbendanê\",\"reşemiyê\",\"adarê\",\"avrêlê\",\"gulanê\",\"pûşperê\",\"tîrmehê\",\"gelawêjê\",\"rezberê\",\"kewçêrê\",\"sermawezê\",\"berfanbarê\"]],[[\"R\",\"R\",\"A\",\"A\",\"G\",\"P\",\"T\",\"G\",\"R\",\"K\",\"S\",\"B\"],[\"rêb\",\"reş\",\"ada\",\"avr\",\"gul\",\"pûş\",\"tîr\",\"gel\",\"rez\",\"kew\",\"ser\",\"ber\"],[\"rêbendan\",\"reşemî\",\"adar\",\"avrêl\",\"gulan\",\"pûşper\",\"tîrmeh\",\"gelawêj\",\"rezber\",\"kewçêr\",\"sermawez\",\"berfanbar\"]],[[\"BZ\",\"PZ\"],u,[\"berî zayînê\",\"piştî zayînê\"]],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"%#,##0\",\"#,##0.00 ¤\",\"#E0\"],\"TRY\",\"₺\",\"TRY\",{\"JPY\":[\"JP¥\",\"¥\"],\"TRY\":[\"₺\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}