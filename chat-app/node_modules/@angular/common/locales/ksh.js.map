{"version": 3, "file": "ksh.js", "sourceRoot": "", "sources": ["ksh.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,WAAW,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,sBAAsB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,MAAM,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 0)\n    return 0;\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ksh\",[[\"v.M.\",\"n.M.\"],u,[\"Uhr vörmiddaachs\",\"Uhr nommendaachs\"]],[[\"v.M.\",\"n.M.\"],u,[\"Vörmeddaach\",\"<PERSON>mmendaach\"]],[[\"S\",\"M\",\"D\",\"M\",\"D\",\"F\",\"S\"],[\"Su.\",\"Mo.\",\"Di.\",\"Me.\",\"Du.\",\"Fr.\",\"Sa.\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\"],[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]],u,[[\"J\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"S\",\"O\",\"N\",\"D\"],[\"<PERSON>\",\"<PERSON><PERSON>b\",\"<PERSON><PERSON>z\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Ouj\",\"<PERSON><PERSON>p\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>z\"],[\"<PERSON>newa\",\"<PERSON><PERSON><PERSON>wa\",\"<PERSON><PERSON><PERSON>z\",\"<PERSON><PERSON>\",\"<PERSON>\",\"Juuni\",\"Juuli\",\"Oujoß\",\"Septämber\",\"Oktohber\",\"Novämber\",\"Dezämber\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"J\",\"O\",\"S\",\"O\",\"N\",\"D\"],[\"Jan.\",\"Fäb.\",\"Mäz.\",\"Apr.\",\"Mai\",\"Jun.\",\"Jul.\",\"Ouj.\",\"Säp.\",\"Okt.\",\"Nov.\",\"Dez.\"],[\"Jannewa\",\"Fäbrowa\",\"Määz\",\"Aprell\",\"Mai\",\"Juuni\",\"Juuli\",\"Oujoß\",\"Septämber\",\"Oktohber\",\"Novämber\",\"Dezämber\"]],[[\"vC\",\"nC\"],[\"v. Chr.\",\"n. Chr.\"],[\"vür Krestos\",\"noh Krestos\"]],1,[6,0],[\"d. M. y\",\"d. MMM. y\",\"d. MMMM y\",\"EEEE, 'dä' d. MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"−\",\"×10^\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"Euro\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}