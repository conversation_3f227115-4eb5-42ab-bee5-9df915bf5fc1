{"version": 3, "file": "th.js", "sourceRoot": "", "sources": ["th.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,YAAY,EAAC,YAAY,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,YAAY,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,YAAY,EAAC,WAAW,EAAC,WAAW,EAAC,QAAQ,EAAC,aAAa,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,YAAY,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,YAAY,EAAC,oBAAoB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,8BAA8B,EAAC,iCAAiC,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"th\",[[\"a\",\"p\"],[\"ก่อนเที่ยง\",\"หลังเที่ยง\"],u],[[\"ก่อนเที่ยง\",\"หลังเที่ยง\"],u,u],[[\"อา\",\"จ\",\"อ\",\"พ\",\"พฤ\",\"ศ\",\"ส\"],[\"อา.\",\"จ.\",\"อ.\",\"พ.\",\"พฤ.\",\"ศ.\",\"ส.\"],[\"วันอาทิตย์\",\"วันจันทร์\",\"วันอังคาร\",\"วันพุธ\",\"วันพฤหัสบดี\",\"วันศุกร์\",\"วันเสาร์\"],[\"อา.\",\"จ.\",\"อ.\",\"พ.\",\"พฤ.\",\"ศ.\",\"ส.\"]],u,[[\"ม.ค.\",\"ก.พ.\",\"มี.ค.\",\"เม.ย.\",\"พ.ค.\",\"มิ.ย.\",\"ก.ค.\",\"ส.ค.\",\"ก.ย.\",\"ต.ค.\",\"พ.ย.\",\"ธ.ค.\"],u,[\"มกราคม\",\"กุมภาพันธ์\",\"มีนาคม\",\"เมษายน\",\"พฤษภาคม\",\"มิถุนายน\",\"กรกฎาคม\",\"สิงหาคม\",\"กันยายน\",\"ตุลาคม\",\"พฤศจิกายน\",\"ธันวาคม\"]],u,[[\"ก่อน ค.ศ.\",\"ค.ศ.\"],u,[\"ปีก่อนคริสตกาล\",\"คริสต์ศักราช\"]],0,[6,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM G y\",\"EEEEที่ d MMMM G y\"],[\"HH:mm\",\"HH:mm:ss\",\"H นาฬิกา mm นาที ss วินาที z\",\"H นาฬิกา mm นาที ss วินาที zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"THB\",\"฿\",\"บาท\",{\"AUD\":[\"AU$\",\"$\"],\"BYN\":[u,\"р.\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"XXX\":[]},\"ltr\", plural];\n"]}