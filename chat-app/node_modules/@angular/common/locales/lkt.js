/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["lkt", [["AM", "PM"], u, u], u, [["A", "W", "N", "Y", "T", "Z", "O"], ["Aŋpétuwakȟaŋ", "Aŋpétuwaŋži", "Aŋp<PERSON>tunuŋpa", "Aŋpétuyamni", "<PERSON>ŋpétutopa", "<PERSON>ŋ<PERSON><PERSON>tuzaptaŋ", "Owáŋgyužažapi"], u, u], [["S", "M", "T", "W", "T", "F", "S"], ["Aŋpétuwakȟaŋ", "Aŋp<PERSON>tuwaŋži", "<PERSON>ŋ<PERSON><PERSON>tunuŋ<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>zapta<PERSON>", "Ow<PERSON>ŋgyužažapi"], u, u], [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Wiótheȟika Wí", "Thiyóȟeyuŋka Wí", "Ištáwičhayazaŋ Wí", "Pȟežítȟo Wí", "Čhaŋwápetȟo Wí", "Wípazukȟa-wašté Wí", "Čhaŋpȟásapa Wí", "Wasútȟuŋ Wí", "Čhaŋwápeǧi Wí", "Čhaŋwápe-kasná Wí", "Waníyetu Wí", "Tȟahékapšuŋ Wí"], u], u, [["BCE", "CE"], u, u], 0, [6, 0], ["M/d/yy", "MMM d, y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "USD", "$", "USD", { "JPY": ["JP¥", "¥"] }, "ltr", plural];
//# sourceMappingURL=lkt.js.map