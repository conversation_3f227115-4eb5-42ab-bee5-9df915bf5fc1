/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["xog", [["<PERSON>nky<PERSON>", "<PERSON>ig<PERSON>"], u, u], u, [["S", "B", "B", "S", "K", "K", "M"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], ["<PERSON>bi<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>wo<PERSON>bil<PERSON>", "Owokusatu", "<PERSON>lokuna", "Olokutaanu", "<PERSON>lomuka<PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "A", "<PERSON>", "O", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Agu", "<PERSON>b", "<PERSON>i", "Nov", "Des"], ["<PERSON>waliyo", "<PERSON>waliyo", "<PERSON>i", "Apuli", "Maayi", "<PERSON>uni", "Julaayi", "Agusito", "<PERSON><PERSON>temba", "<PERSON><PERSON><PERSON>", "Novemba", "<PERSON>emba"]], u, [["A<PERSON>", "A<PERSON>"], u, ["<PERSON>listo nga azilawo", "Kulisto nga affile"]], 1, [0, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "UGX", "USh", "Silingi eya Yuganda", { "JPY": ["JP¥", "¥"], "UGX": ["USh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=xog.js.map