{"version": 3, "file": "sat.js", "sourceRoot": "", "sources": ["sat.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,YAAY,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,kBAAkB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nif (n === 2)\n    return 2;\nreturn 5;\n}\n\nexport default [\"sat\",[[\"AM\",\"PM\"],u,[\"ᱥᱮᱛᱟᱜ\",\"ᱧᱤᱫᱟᱹ\"]],[[\"AM\",\"PM\"],u,u],[[\"ᱥ\",\"ᱚ\",\"ᱵ\",\"ᱥ\",\"ᱥ\",\"ᱡ\",\"ᱧ\"],[\"ᱥᱤᱸ\",\"ᱚᱛ\",\"ᱵᱟ\",\"ᱥᱟᱹ\",\"ᱥᱟᱹᱨ\",\"ᱡᱟᱹ\",\"ᱧᱩ\"],[\"ᱥᱤᱸᱜᱮ\",\"ᱚᱛᱮ\",\"ᱵᱟᱞᱮ\",\"ᱥᱟᱹᱜᱩᱱ\",\"ᱥᱟᱹᱨᱫᱤ\",\"ᱡᱟᱹᱨᱩᱢ\",\"ᱧᱩᱦᱩᱢ\"],[\"ᱥᱤᱸ\",\"ᱚᱛ\",\"ᱵᱟ\",\"ᱥᱟᱹ\",\"ᱥᱟᱹᱨ\",\"ᱡᱟᱹ\",\"ᱧᱩ\"]],u,[[\"ᱡ\",\"ᱯ\",\"ᱢ\",\"ᱟ\",\"ᱢ\",\"ᱡ\",\"ᱡ\",\"ᱟ\",\"ᱥ\",\"ᱚ\",\"ᱱ\",\"ᱫ\"],[\"ᱡᱟᱱ\",\"ᱯᱷᱟ\",\"ᱢᱟᱨ\",\"ᱟᱯᱨ\",\"ᱢᱮ\",\"ᱡᱩᱱ\",\"ᱡᱩᱞ\",\"ᱟᱜᱟ\",\"ᱥᱮᱯ\",\"ᱚᱠᱴ\",\"ᱱᱟᱣ\",\"ᱫᱤᱥ\"],[\"ᱡᱟᱱᱣᱟᱨᱤ\",\"ᱯᱷᱟᱨᱣᱟᱨᱤ\",\"ᱢᱟᱨᱪ\",\"ᱟᱯᱨᱮᱞ\",\"ᱢᱮ\",\"ᱡᱩᱱ\",\"ᱡᱩᱞᱟᱭ\",\"ᱟᱜᱟᱥᱛ\",\"ᱥᱮᱯᱴᱮᱢᱵᱟᱨ\",\"ᱚᱠᱴᱚᱵᱟᱨ\",\"ᱱᱟᱣᱟᱢᱵᱟᱨ\",\"ᱫᱤᱥᱟᱢᱵᱟᱨ\"]],u,[[\"ᱥᱮᱨᱢᱟ ᱞᱟᱦᱟ\",\"ᱤᱥᱣᱤ\"],u,u],0,[0,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"INR\",\"₹\",\"ᱥᱤᱧᱚᱛ ᱨᱮᱱᱟᱜ ᱴᱟᱠᱟ\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}