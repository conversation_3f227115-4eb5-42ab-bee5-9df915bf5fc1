{"version": 3, "file": "tk.js", "sourceRoot": "", "sources": ["tk.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,eAAe,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,eAAe,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,SAAS,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"tk\",[[\"öň\",\"soň\"],[\"go.öň\",\"go.soň\"],[\"günortadan ö<PERSON>\",\"günortadan soň\"]],[[\"öň\",\"soň\"],[\"g.öň\",\"g.soň\"],[\"günortadan öň\",\"günortadan soň\"]],[[\"Ý\",\"D\",\"S\",\"Ç\",\"P\",\"A\",\"Ş\"],[\"ýek\",\"duş\",\"siş\",\"çar\",\"pen\",\"ann\",\"şen\"],[\"ýekşenbe\",\"duşenbe\",\"sişenbe\",\"çarşenbe\",\"penşenbe\",\"anna\",\"şenbe\"],[\"ýb\",\"db\",\"sb\",\"çb\",\"pb\",\"an\",\"şb\"]],[[\"Ý\",\"D\",\"S\",\"Ç\",\"<PERSON>\",\"A\",\"Ş\"],[\"Ýek\",\"Duş\",\"Siş\",\"Çar\",\"Pen\",\"Ann\",\"Şen\"],[\"Ýekşenbe\",\"Duşenbe\",\"Sişenbe\",\"Çarşenbe\",\"Penşenbe\",\"Anna\",\"Şenbe\"],[\"Ýb\",\"Db\",\"Sb\",\"Çb\",\"Pb\",\"An\",\"Şb\"]],[[\"Ý\",\"F\",\"M\",\"A\",\"M\",\"I\",\"I\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"ýan\",\"few\",\"mart\",\"apr\",\"maý\",\"iýun\",\"iýul\",\"awg\",\"sen\",\"okt\",\"noý\",\"dek\"],[\"ýanwar\",\"fewral\",\"mart\",\"aprel\",\"maý\",\"iýun\",\"iýul\",\"awgust\",\"sentýabr\",\"oktýabr\",\"noýabr\",\"dekabr\"]],[[\"Ý\",\"F\",\"M\",\"A\",\"M\",\"I\",\"I\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"Ýan\",\"Few\",\"Mar\",\"Apr\",\"Maý\",\"Iýun\",\"Iýul\",\"Awg\",\"Sen\",\"Okt\",\"Noý\",\"Dek\"],[\"Ýanwar\",\"Fewral\",\"Mart\",\"Aprel\",\"Maý\",\"Iýun\",\"Iýul\",\"Awgust\",\"Sentýabr\",\"Oktýabr\",\"Noýabr\",\"Dekabr\"]],[[\"B.e.öň\",\"B.e.\"],u,[\"Isadan öň\",\"Isadan soň\"]],1,[6,0],[\"dd.MM.y\",\"d MMM y\",\"d MMMM y\",\"d MMMM y EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"san däl\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"TMT\",\"TMT\",\"Türkmen manady\",{\"BYN\":[u,\"р.\"],\"EUR\":[u,\"€\"],\"GBP\":[u,\"£\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}