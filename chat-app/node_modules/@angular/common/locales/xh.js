/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["xh", [["AM", "PM"], u, u], u, [["S", "M", "T", "W", "T", "F", "S"], ["Caw", "Mvu", "<PERSON>", "<PERSON>ha", "Sin", "<PERSON>la", "Mgq"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "L<PERSON><PERSON>lanu", "Mgqibelo"], ["<PERSON><PERSON>", "Mvu", "<PERSON>", "<PERSON>ha", "<PERSON>", "<PERSON>la", "Mgq"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["<PERSON>", "Feb", "<PERSON>", "<PERSON>p<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>s"], ["<PERSON>yu<PERSON>", "<PERSON>ru<PERSON>", "<PERSON><PERSON>", "<PERSON>p<PERSON>i", "<PERSON>yi", "<PERSON>i", "<PERSON>ayi", "<PERSON><PERSON><PERSON>", "<PERSON>emba", "<PERSON>th<PERSON>a", "<PERSON>em<PERSON>", "<PERSON><PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>"], u, u], 0, [6, 0], ["y-<PERSON>M-dd", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "ZAR", "R", "iRandi yaseMzanzi Afrika", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"], "ZAR": ["R"] }, "ltr", plural];
//# sourceMappingURL=xh.js.map