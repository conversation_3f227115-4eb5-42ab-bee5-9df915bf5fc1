/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["lo", [["ກ່ອນທ່ຽງ", "ຫຼັງທ່ຽງ"], u, u], u, [["ອາ", "ຈ", "ອ", "ພ", "ພຫ", "ສຸ", "ສ"], ["ອາທິດ", "ຈັນ", "ອັງຄານ", "ພຸດ", "ພະຫັດ", "ສຸກ", "ເສົາ"], ["ວັນອາທິດ", "ວັນຈັນ", "ວັນອັງຄານ", "ວັນພຸດ", "ວັນພະຫັດ", "ວັນສຸກ", "ວັນເສົາ"], ["ອາ.", "ຈ.", "ອ.", "ພ.", "ພຫ.", "ສຸ.", "ສ."]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["ມ.ກ.", "ກ.ພ.", "ມ.ນ.", "ມ.ສ.", "ພ.ພ.", "ມິ.ຖ.", "ກ.ລ.", "ສ.ຫ.", "ກ.ຍ.", "ຕ.ລ.", "ພ.ຈ.", "ທ.ວ."], ["ມັງກອນ", "ກຸມພາ", "ມີນາ", "ເມສາ", "ພຶດສະພາ", "ມິຖຸນາ", "ກໍລະກົດ", "ສິງຫາ", "ກັນຍາ", "ຕຸລາ", "ພະຈິກ", "ທັນວາ"]], u, [["ກ່ອນ ຄ.ສ.", "ຄ.ສ."], u, ["ກ່ອນຄຣິດສັກກະລາດ", "ຄຣິດສັກກະລາດ"]], 0, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE ທີ d MMMM G y"], ["H:mm", "H:mm:ss", "H ໂມງ m ນາທີ ss ວິນາທີ z", "H ໂມງ m ນາທີ ss ວິນາທີ zzzz"], ["{1}, {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "ບໍ່​ແມ່ນ​ໂຕ​ເລກ", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00;¤-#,##0.00", "#"], "LAK", "₭", "ລາວ ກີບ", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "LAK": ["₭"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=lo.js.map