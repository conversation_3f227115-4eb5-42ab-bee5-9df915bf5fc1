/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["yrl-VE", [["a. m.", "p. m."], u, u], u, [["M", "M", "M", "M", "S", "Y", "S"], ["mit", "mur", "mmk", "mms", "sup", "yuk", "sau"], ["mituú", "murakipí", "murakí-mukũi", "murakí-musapíri", "supapá", "yukuakú", "saurú"], ["mit", "mur", "mmk", "mms", "sup", "yuk", "sau"]], u, [["Y", "M", "M", "I", "P", "P", "P", "P", "P", "Y", "Y", "Y"], ["ye", "mk", "ms", "id", "pu", "py", "pm", "ps", "pi", "yp", "yy", "ym"], ["yepé", "mukũi", "musapíri", "irũdí", "pú", "pú-yepé", "pú-mukũi", "pú-musapíri", "pú-irũdí", "yepé-putimaã", "yepé-yepé", "yepé-mukũi"]], u, [["K.s.", "K.a."], u, ["Kiristu senũdé", "Kiristu ariré"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "VES", "Bs.S", "Buriwari Wenesuerawara", { "AUD": ["AU$", "$"], "BOB": ["BUB", "Bs"], "BYN": [u, "p."], "COP": ["$", "COP"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "PTE": ["Esc."], "RON": [u, "L"], "SCR": ["SCRu"], "SYP": [u, "S£"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "VES": ["Bs.S", "VES"], "XAF": ["FCF"], "XOF": ["CFA"], "XPF": ["CFP"], "ZMW": [u, "Zk"] }, "ltr", plural];
//# sourceMappingURL=yrl-VE.js.map