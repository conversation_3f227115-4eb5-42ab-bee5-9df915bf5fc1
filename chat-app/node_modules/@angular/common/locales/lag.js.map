{"version": 3, "file": "lag.js", "sourceRoot": "", "sources": ["lag.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,KAAK,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,WAAW,EAAC,SAAS,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,cAAc,EAAC,YAAY,EAAC,WAAW,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,sBAAsB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,wBAAwB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (n === 0)\n    return 0;\nif ((i === 0 || i === 1) && !(n === 0))\n    return 1;\nreturn 5;\n}\n\nexport default [\"lag\",[[\"TOO\",\"MUU\"],u,u],u,[[\"P\",\"T\",\"E\",\"O\",\"A\",\"I\",\"M\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>atu\",\"<PERSON>ne\",\"Tá<PERSON>\",\"Alh\",\"Ijm\",\"Móos<PERSON>\"],[\"<PERSON><PERSON><PERSON><PERSON>iri\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Jumaíne\",\"Jumatáano\",\"<PERSON>am<PERSON><PERSON>\",\"<PERSON>jum<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>h\",\"Ijm\",\"<PERSON><PERSON><PERSON><PERSON>\"]],u,[[\"F\",\"N\",\"<PERSON>\",\"I\",\"I\",\"I\",\"M\",\"V\",\"S\",\"I\",\"S\",\"S\"],[\"Fúngatɨ\",\"Naanɨ\",\"Keenda\",\"Ikúmi\",\"Inyambala\",\"Idwaata\",\"Mʉʉnchɨ\",\"Vɨɨrɨ\",\"Saatʉ\",\"Inyi\",\"Saano\",\"Sasatʉ\"],[\"Kʉfúngatɨ\",\"Kʉnaanɨ\",\"Kʉkeenda\",\"Kwiikumi\",\"Kwiinyambála\",\"Kwiidwaata\",\"Kʉmʉʉnchɨ\",\"Kʉvɨɨrɨ\",\"Kʉsaatʉ\",\"Kwiinyi\",\"Kʉsaano\",\"Kʉsasatʉ\"]],u,[[\"KSA\",\"KA\"],u,[\"Kɨrɨsitʉ sɨ anavyaal\",\"Kɨrɨsitʉ akavyaalwe\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"TZS\",\"TSh\",\"Shilíingi ya Taansanía\",{\"JPY\":[\"JP¥\",\"¥\"],\"TZS\":[\"TSh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}