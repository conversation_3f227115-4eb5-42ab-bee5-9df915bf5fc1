/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["lu", [["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], u, u], u, [["L", "N", "N", "N", "N", "N", "L"], ["Lum", "Nko", "Ndy", "Ndg", "Njw", "Ngv", "Lub"], ["<PERSON><PERSON><PERSON>", "<PERSON>ko<PERSON>a", "Nd<PERSON><PERSON><PERSON>", "Ndang<PERSON>", "Nj<PERSON><PERSON>", "<PERSON><PERSON><PERSON>a", "<PERSON>bing<PERSON>"], ["Lum", "Nko", "Ndy", "Ndg", "Njw", "Ngv", "Lub"]], u, [["C", "L", "L", "M", "L", "L", "K", "L", "L", "L", "<PERSON>", "<PERSON>"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lu<PERSON>", "Lu<PERSON>", "<PERSON><PERSON>", "<PERSON>sh", "<PERSON>t", "<PERSON>n", "Kas", "<PERSON>is"], ["<PERSON>iongo", "Lùishi", "Lusòlo", "<PERSON>ùuyà", "Lumùngùlù", "Lufuimi", "Kabàlàsh<PERSON>pù", "Lùsh<PERSON>k<PERSON>", "<PERSON>golo", "Lungùdi", "Kaswèk<PERSON>è", "Ciswà"]], u, [["kmp. Y.K.", "kny. Y. K."], u, ["Kumpala kwa Yezu Kli", "Kunyima kwa Yezu Kli"]], 1, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "CDF", "FC", "Nfalanga wa Kongu", { "CDF": ["FC"], "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=lu.js.map