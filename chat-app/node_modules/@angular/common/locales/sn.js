/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["sn", [["a", "p"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["S", "M", "C", "C", "C", "C", "M"], ["Svo", "Muv", "Chp", "Cht", "Chn", "Chs", "Mug"], ["<PERSON>von<PERSON>", "<PERSON>vhuro", "Chip<PERSON>", "Chitatu", "China", "Chishanu", "Mugovera"], ["Sv", "Mu", "Cp", "Ct", "Cn", "Cs", "Mg"]], u, [["N", "K", "K", "K", "C", "C", "C", "N", "G", "G", "M", "Z"], ["Ndi", "Kuk", "Kur", "Kub", "Chv", "<PERSON>k", "<PERSON>g", "<PERSON>ya", "<PERSON>", "Gum", "Mbu", "<PERSON>vi"], ["N<PERSON>", "<PERSON>kadzi", "<PERSON>rume", "<PERSON>bvumbi", "<PERSON>vabvu", "<PERSON>kumi", "<PERSON>kunguru", "<PERSON>yamavhuvhu", "<PERSON>yana", "<PERSON><PERSON>guru", "<PERSON>budzi", "<PERSON>vita"]], u, [["BC", "AD"], u, ["Kristo asati auya", "mugore ramambo vedu"]], 0, [6, 0], ["y-MM-dd", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "USD", "US$", "Dora re Amerika", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=sn.js.map