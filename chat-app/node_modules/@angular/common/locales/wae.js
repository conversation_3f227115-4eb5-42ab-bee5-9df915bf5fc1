/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["wae", [["AM", "PM"], u, u], u, [["S", "M", "Z", "M", "F", "F", "S"], ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], ["Sunntag", "Mäntag", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Frón<PERSON>", "Fritag", "Samš<PERSON>"], ["Sun", "<PERSON>ä<PERSON>", "<PERSON>i<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>i", "<PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "W", "W", "<PERSON>"], ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>b<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>ig", "<PERSON>", "<PERSON><PERSON>m", "Win", "Chr"], ["Jenner", "Hornig", "Märze", "Abrille", "Meije", "Br<PERSON>čet", "Heiwet", "Öigšte", "Herbštmánet", "<PERSON><PERSON><PERSON>et", "<PERSON>mánet", "Chrištmánet"]], u, [["v. Chr.", "n. Chr"], u, u], 1, [6, 0], ["y-MM-dd", "d. MMM y", "d. MMMM y", "EEEE, d. MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", "’", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "CHF", "CHF", "CHF", {}, "ltr", plural];
//# sourceMappingURL=wae.js.map