/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["dav", [["Luma lwa K", "luma lwa p"], u, u], u, [["J", "J", "K", "K", "K", "K", "N"], ["<PERSON><PERSON>", "<PERSON>", "<PERSON>w", "Kad", "Kan", "Ka<PERSON>", "<PERSON>u"], ["<PERSON>uku ja jumwa", "<PERSON><PERSON><PERSON> jimweri", "<PERSON><PERSON>uka kawi", "<PERSON><PERSON>uka kadadu", "<PERSON><PERSON>uka kana", "<PERSON><PERSON><PERSON> kasanu", "<PERSON><PERSON>a nguwo"], ["Jum", "<PERSON>", "<PERSON>w", "Kad", "Kan", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "W", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["Imb", "Kaw", "Kad", "Kan", "<PERSON>s", "Kar", "<PERSON>fu", "<PERSON>n", "Ike", "Iku", "Imw", "Iwi"], ["<PERSON>ri ghwa imbiri", "<PERSON>ri ghwa kawi", "<PERSON>ri ghwa kadadu", "<PERSON>ri ghwa kana", "<PERSON>ri ghwa kasanu", "<PERSON>ri ghwa ka<PERSON>adu", "<PERSON>ri ghwa mfungade", "<PERSON>ri ghwa wunyanya", "<PERSON>ri ghwa ikenda", "Mori ghwa ikumi", "Mori ghwa ikumi na imweri", "Mori ghwa ikumi na iwi"]], u, [["KK", "BK"], u, ["Kabla ya Kristo", "Baada ya Kristo"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Shilingi ya Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=dav.js.map