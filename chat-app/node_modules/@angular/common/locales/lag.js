/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val));
    if (n === 0)
        return 0;
    if ((i === 0 || i === 1) && !(n === 0))
        return 1;
    return 5;
}
export default ["lag", [["TOO", "MUU"], u, u], u, [["P", "T", "E", "O", "A", "I", "M"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>atu", "<PERSON>ne", "<PERSON><PERSON><PERSON>", "<PERSON>h", "Ijm", "Móos<PERSON>"], ["<PERSON><PERSON>p<PERSON>iri", "<PERSON><PERSON><PERSON><PERSON>", "Jumaíne", "Jumatáano", "Alam<PERSON><PERSON>", "<PERSON>jum<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>h", "Ijm", "<PERSON><PERSON><PERSON><PERSON>"]], u, [["F", "N", "<PERSON>", "<PERSON>", "I", "I", "M", "V", "S", "I", "S", "S"], ["Fúngatɨ", "Naanɨ", "Keenda", "Ikúmi", "Inyambala", "Idwaata", "Mʉʉnchɨ", "Vɨɨrɨ", "Saatʉ", "Inyi", "Saano", "Sasatʉ"], ["Kʉfúngatɨ", "Kʉnaanɨ", "Kʉkeenda", "Kwiikumi", "Kwiinyambála", "Kwiidwaata", "Kʉmʉʉnchɨ", "Kʉvɨɨrɨ", "Kʉsaatʉ", "Kwiinyi", "Kʉsaano", "Kʉsasatʉ"]], u, [["KSA", "KA"], u, ["Kɨrɨsitʉ sɨ anavyaal", "Kɨrɨsitʉ akavyaalwe"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "TZS", "TSh", "Shilíingi ya Taansanía", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=lag.js.map