/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["ms-SG", [["a", "p"], ["PG", "PTG"], u], u, [["A", "I", "S", "R", "K", "J", "S"], ["Ahd", "Isn", "Sel", "Rab", "<PERSON>ha", "Ju<PERSON>", "Sab"], ["Ahad", "<PERSON>in", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ma<PERSON>", "Sabtu"], ["Ah", "Is", "Se", "Ra", "Kh", "<PERSON>", "Sa"]], u, [["J", "F", "M", "A", "M", "J", "J", "O", "<PERSON>", "O", "<PERSON>", "D"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "O<PERSON>", "<PERSON>", "Okt", "Nov", "<PERSON>s"], ["<PERSON>uari", "Februari", "<PERSON>", "April", "<PERSON>", "Jun", "Julai", "O<PERSON>", "September", "Oktober", "November", "Disember"]], u, [["S.M.", "TM"], u, u], 0, [6, 0], ["d/MM/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "SGD", "$", "Dolar Singapura", { "BYN": [u, "р."], "CAD": [u, "$"], "JPY": ["JP¥", "¥"], "MXN": [u, "$"], "MYR": ["RM"], "PHP": [u, "₱"], "SGD": ["$"], "TWD": ["NT$"], "USD": [u, "$"] }, "ltr", plural];
//# sourceMappingURL=ms-SG.js.map