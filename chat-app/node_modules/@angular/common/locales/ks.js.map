{"version": 3, "file": "ks.js", "sourceRoot": "", "sources": ["ks.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,YAAY,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,gBAAgB,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,kBAAkB,EAAC,EAAE,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ks\",[[\"AM\",\"PM\"],u,u],u,[[\"ا\",\"ژ\",\"ب\",\"ب\",\"ب\",\"ج\",\"ب\"],[\"آتھوار\",\"ژٔندٕروار\",\"بۆموار\",\"بودوار\",\"برؠسوار\",\"جُمہ\",\"بٹوار\"],[\"اَتھوار\",\"ژٔندرٕروار\",\"بۆموار\",\"بودوار\",\"برؠسوار\",\"جُمہ\",\"بٹوار\"],[\"آتھوار\",\"ژٔندٕروار\",\"بۆموار\",\"بودوار\",\"برؠسوار\",\"جُمہ\",\"بٹوار\"]],u,[[\"ج\",\"ف\",\"م\",\"ا\",\"م\",\"ج\",\"ج\",\"ا\",\"س\",\"س\",\"ا\",\"ن\"],[\"جنؤری\",\"فرؤری\",\"مارٕچ\",\"اپریل\",\"مئی\",\"جوٗن\",\"جوٗلایی\",\"اگست\",\"ستمبر\",\"اکتوٗبر\",\"نومبر\",\"دسمبر\"],u],u,[[\"بی سی\",\"اے ڈی\"],u,[\"قبٕل مسیٖح\",\"عیٖسوی سنہٕ\"]],0,[0,0],[\"M/d/yy\",\"MMM d, y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{0} پٮ۪ٹھۍ {1}\",u],[\".\",\"،\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"INR\",\"₹\",\"ہِندُستٲنۍ رۄپَے\",{},\"rtl\", plural];\n"]}