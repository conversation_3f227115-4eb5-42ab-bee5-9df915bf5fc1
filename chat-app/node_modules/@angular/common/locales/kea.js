/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["kea", [["am", "pm"], u, u], u, [["D", "S", "T", "K", "K", "S", "S"], ["dum", "sig", "ter", "kua", "kin", "ses", "sab"], ["dumingu", "sigunda-fera", "tersa-fera", "kuarta-fera", "kinta-fera", "sesta-fera", "sábadu"], ["du", "si", "te", "ku", "ki", "se", "sa"]], u, [["J", "F", "M", "A", "<PERSON>", "<PERSON>", "J", "A", "<PERSON>", "O", "N", "D"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>br", "<PERSON>", "<PERSON>", "<PERSON>", "A<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>uv", "<PERSON>z"], ["<PERSON>ru", "<PERSON><PERSON>u", "<PERSON>u", "<PERSON>bril", "<PERSON>u", "Junhu", "<PERSON>hu", "A<PERSON>tu", "<PERSON>enbru", "Otubru", "Nuvenbru", "<PERSON>zenbru"]], u, [["<PERSON>", "D<PERSON>"], u, ["antis di <PERSON>tu", "dispos di <PERSON>tu"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d 'di' MMMM 'di' y", "EEEE, d 'di' MMMM 'di' y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "CVE", "​", "Skudu Kabuverdianu", { "AUD": ["AU$", "$"], "CVE": ["​"], "JPY": ["JP¥", "¥"], "THB": ["฿"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=kea.js.map