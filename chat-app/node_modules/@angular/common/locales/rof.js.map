{"version": 3, "file": "rof.js", "sourceRoot": "", "sources": ["rof.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,iBAAiB,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,gBAAgB,EAAC,eAAe,EAAC,eAAe,EAAC,eAAe,EAAC,eAAe,EAAC,eAAe,EAAC,gBAAgB,EAAC,wBAAwB,EAAC,yBAAyB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,oBAAoB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"rof\",[[\"kang’ama\",\"king<PERSON>\"],u,u],u,[[\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"1\"],[\"Ijp\",\"Ijt\",\"Ijn\",\"Ijtn\",\"<PERSON>h\",\"<PERSON>ju\",\"Ijm\"],[\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"Ijumanne\",\"Ijumatano\",\"<PERSON>ham<PERSON>\",\"Ijumaa\",\"Ijumamosi\"],[\"Ijp\",\"Ijt\",\"Ijn\",\"Ijtn\",\"<PERSON>h\",\"<PERSON><PERSON>\",\"Ijm\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"K\",\"K\",\"T\",\"S\",\"S\",\"N\",\"T\",\"I\",\"I\",\"I\"],[\"M1\",\"M2\",\"M3\",\"M4\",\"M5\",\"M6\",\"M7\",\"M8\",\"M9\",\"M10\",\"M11\",\"M12\"],[\"Mweri wa kwanza\",\"Mweri wa kaili\",\"Mweri wa katatu\",\"Mweri wa kaana\",\"Mweri wa tanu\",\"Mweri wa sita\",\"Mweri wa saba\",\"Mweri wa nane\",\"Mweri wa tisa\",\"Mweri wa ikumi\",\"Mweri wa ikumi na moja\",\"Mweri wa ikumi na mbili\"]],u,[[\"KM\",\"BM\"],u,[\"Kabla ya Mayesu\",\"Baada ya Mayesu\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"TZS\",\"TSh\",\"heleri sa Tanzania\",{\"JPY\":[\"JP¥\",\"¥\"],\"TZS\":[\"TSh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}