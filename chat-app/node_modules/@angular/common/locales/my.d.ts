/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    ANG: string[];
    AWG: string[];
    BBD: (string | undefined)[];
    BSD: (string | undefined)[];
    BYN: (string | undefined)[];
    HTG: string[];
    JPY: string[];
    MMK: string[];
    PAB: string[];
    PHP: (string | undefined)[];
    THB: string[];
    TTD: string[];
    USD: string[];
} | undefined)[];
export default _default;
