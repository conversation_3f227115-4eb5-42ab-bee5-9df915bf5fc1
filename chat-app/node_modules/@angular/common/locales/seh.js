/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["seh", [["AM", "PM"], u, u], u, [["D", "P", "C", "T", "N", "S", "S"], ["Dim", "<PERSON>s", "<PERSON>r", "Tat", "Nai", "<PERSON>ha", "Sab"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>u", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sa<PERSON><PERSON>"], ["Dim", "<PERSON><PERSON>", "<PERSON>r", "Tat", "Nai", "<PERSON>ha", "Sab"]], u, [["J", "F", "M", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "O", "N", "<PERSON>"], ["<PERSON>", "<PERSON>v", "<PERSON>", "<PERSON>b<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>tu", "Nov", "<PERSON>"], ["Janeiro", "Fevreiro", "<PERSON>", "<PERSON>bril", "<PERSON>o", "<PERSON>ho", "<PERSON>ho", "Augusto", "<PERSON>embro", "<PERSON>tubro", "<PERSON>embro", "<PERSON>embro"]], u, [["<PERSON>", "AD"], u, ["<PERSON><PERSON> de <PERSON><PERSON>", "<PERSON>o <PERSON><PERSON>"]], 0, [6, 0], ["d/<PERSON>/y", "d 'de' MMM 'de' y", "d 'de' MMMM 'de' y", "EEEE, d 'de' MMMM 'de' y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "MZN", "MTn", "Metical de Moçambique", { "JPY": ["JP¥", "¥"], "MZN": ["MTn"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=seh.js.map