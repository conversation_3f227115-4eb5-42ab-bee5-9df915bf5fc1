/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["lg", [["AM", "PM"], u, u], u, [["S", "B", "L", "L", "L", "L", "L"], ["Sab", "Bal", "Lw2", "Lw3", "Lw4", "Lw5", "Lw6"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "L<PERSON><PERSON><PERSON><PERSON>", "Lwakusatu", "Lwa<PERSON><PERSON>", "Lwakutaano", "Lwamuka<PERSON>"], ["Sab", "Bal", "Lw2", "Lw3", "Lw4", "Lw5", "Lw6"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "A", "M", "J", "J", "A", "S", "O", "N", "D"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>a", "<PERSON>u", "<PERSON>", "<PERSON>gu", "<PERSON>b", "<PERSON>i", "Nov", "<PERSON>"], ["<PERSON>waliyo", "<PERSON>waliyo", "<PERSON>i", "<PERSON>puli", "<PERSON>ayi", "<PERSON><PERSON>", "<PERSON>aayi", "A<PERSON>ito", "<PERSON><PERSON><PERSON>ba", "<PERSON>ito<PERSON>", "<PERSON>emba", "<PERSON>emba"]], u, [["<PERSON>", "<PERSON>"], u, ["Kulisito nga tannaza", "Bukya Kulisito Azaal"]], 1, [0, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "UGX", "USh", "Silingi eya Yuganda", { "JPY": ["JP¥", "¥"], "UGX": ["USh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=lg.js.map