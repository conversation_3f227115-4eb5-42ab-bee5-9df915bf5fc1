{"version": 3, "file": "mas-TZ.js", "sourceRoot": "", "sources": ["mas-TZ.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,QAAQ,EAAC,CAAC,CAAC,WAAW,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,UAAU,EAAC,OAAO,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,MAAM,EAAC,YAAY,EAAC,qBAAqB,EAAC,sBAAsB,EAAC,UAAU,EAAC,WAAW,EAAC,eAAe,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,uBAAuB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"mas-TZ\",[[\"Ɛnkakɛnyá\",\"Ɛndámâ\"],u,u],u,[[\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"1\"],[\"Jpi\",\"Jtt\",\"Jnn\",\"Jtn\",\"<PERSON>h\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"],[\"Jumap<PERSON>l<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"Jumat<PERSON>ɔ\",\"Alaámisi\",\"Jumáa\",\"Jumamósi\"],[\"Jpi\",\"Jtt\",\"Jnn\",\"Jtn\",\"<PERSON>h\",\"<PERSON>ju\",\"<PERSON><PERSON>\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"<PERSON>\",\"Ar<PERSON>\",\"Ɔɛn\",\"<PERSON><PERSON>\",\"<PERSON>ép\",\"Rok\",\"Sás\",\"Bɔ́r\",\"Kús\",\"Gís\",\"Shʉ́\",\"Ntʉ́\"],[\"Oladalʉ́\",\"Arát\",\"Ɔɛnɨ́ɔɨŋɔk\",\"Olodoyíóríê inkókúâ\",\"Oloilépūnyīē inkókúâ\",\"Kújúɔrɔk\",\"Mórusásin\",\"Ɔlɔ́ɨ́bɔ́rárɛ\",\"Kúshîn\",\"Olgísan\",\"Pʉshʉ́ka\",\"Ntʉ́ŋʉ́s\"]],u,[[\"MY\",\"EY\"],u,[\"Meínō Yɛ́sʉ\",\"Eínō Yɛ́sʉ\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"TZS\",\"TSh\",\"Iropiyianí e Tanzania\",{\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"TZS\":[\"TSh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}