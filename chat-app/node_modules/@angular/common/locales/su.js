/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["su", [["AM", "PM"], u, u], u, [["M", "S", "S", "R", "K", "J", "S"], ["Mng", "<PERSON>", "<PERSON>", "<PERSON>b", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sap"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saptu"], ["Mng", "<PERSON>", "<PERSON>", "Reb", "Kem", "Jum", "Sap"]], u, [["J", "P", "M", "A", "M", "J", "J", "A", "S", "O", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON>éb", "<PERSON>", "<PERSON>", "<PERSON>é<PERSON>", "<PERSON>", "<PERSON>", "Ags", "<PERSON>ép", "Okt", "<PERSON>p", "<PERSON><PERSON>"], ["<PERSON>uari", "<PERSON><PERSON>bruari", "<PERSON>t", "April", "<PERSON><PERSON>i", "Juni", "Juli", "Agustus", "Séptémber", "Oktober", "Nopémber", "D<PERSON>émber"]], u, [["SM", "<PERSON>"], u, u], 0, [6, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["H.mm", "H.mm.ss", "H.mm.ss z", "H.mm.ss zzzz"], ["{1}, {0}", u, "{1} 'jam' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", "."], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "IDR", "Rp", "Rupee Indonésia", { "IDR": ["Rp"] }, "ltr", plural];
//# sourceMappingURL=su.js.map