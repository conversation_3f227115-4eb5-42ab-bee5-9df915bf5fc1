/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["sw", [["am", "pm"], ["AM", "PM"], u], [["AM", "PM"], u, u], [["S", "M", "T", "W", "T", "F", "S"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jumann<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ju<PERSON><PERSON>", "Ju<PERSON><PERSON><PERSON>"], u, u], u, [["<PERSON>", "<PERSON>", "<PERSON>", "A", "<PERSON>", "<PERSON>", "<PERSON>", "A", "S", "O", "N", "D"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>"], ["<PERSON>ua<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Aprili", "<PERSON>", "<PERSON>i", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>emba", "<PERSON>toba", "<PERSON>em<PERSON>", "<PERSON>em<PERSON>"]], u, [["<PERSON><PERSON>", "<PERSON><PERSON>"], u, ["<PERSON><PERSON> ya <PERSON>to", "<PERSON>ada ya <PERSON>to"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "TZS", "TSh", "Shilingi ya Tanzania", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "PHP": [u, "₱"], "THB": ["฿"], "TWD": ["NT$"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=sw.js.map