{"version": 3, "file": "yrl-CO.js", "sourceRoot": "", "sources": ["yrl-CO.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,QAAQ,EAAC,CAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,cAAc,EAAC,iBAAiB,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,UAAU,EAAC,OAAO,EAAC,IAAI,EAAC,SAAS,EAAC,UAAU,EAAC,aAAa,EAAC,UAAU,EAAC,cAAc,EAAC,WAAW,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"yrl-CO\",[[\"a. m.\",\"p. m.\"],u,u],u,[[\"M\",\"M\",\"M\",\"M\",\"S\",\"Y\",\"S\"],[\"mit\",\"mur\",\"mmk\",\"mms\",\"sup\",\"yuk\",\"sau\"],[\"mituú\",\"murakipí\",\"murakí-mukũi\",\"murakí-musapíri\",\"supapá\",\"yukuakú\",\"saurú\"],[\"mit\",\"mur\",\"mmk\",\"mms\",\"sup\",\"yuk\",\"sau\"]],u,[[\"Y\",\"M\",\"M\",\"I\",\"P\",\"P\",\"P\",\"P\",\"P\",\"Y\",\"Y\",\"Y\"],[\"ye\",\"mk\",\"ms\",\"id\",\"pu\",\"py\",\"pm\",\"ps\",\"pi\",\"yp\",\"yy\",\"ym\"],[\"yepé\",\"mukũi\",\"musapíri\",\"irũdí\",\"pú\",\"pú-yepé\",\"pú-mukũi\",\"pú-musapíri\",\"pú-irũdí\",\"yepé-putimaã\",\"yepé-yepé\",\"yepé-mukũi\"]],u,[[\"K.s.\",\"K.a.\"],u,[\"Kiristu senũdé\",\"Kiristu ariré\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"COP\",\"$\",\"Peso Kurũbiyawara\",{\"AUD\":[\"AU$\",\"$\"],\"BOB\":[\"BUB\",\"Bs\"],\"BYN\":[u,\"p.\"],\"COP\":[\"$\",\"COP\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"PTE\":[\"Esc.\"],\"RON\":[u,\"L\"],\"SCR\":[\"SCRu\"],\"SYP\":[u,\"S£\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"VES\":[\"Bs.S\",\"VES\"],\"XAF\":[\"FCF\"],\"XOF\":[\"CFA\"],\"XPF\":[\"CFP\"],\"ZMW\":[u,\"Zk\"]},\"ltr\", plural];\n"]}