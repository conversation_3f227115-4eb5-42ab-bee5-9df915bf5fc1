/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['bm'] = ["bm",[["AM","PM"],u,u],u,[["K","N","T","A","A","J","S"],["kar","ntɛ","tar","ara","ala","jum","sib"],["kari","ntɛnɛ","tarata","araba","alamisa","juma","sibiri"],["kar","ntɛ","tar","ara","ala","jum","sib"]],u,[["Z","F","M","A","M","Z","Z","U","S","Ɔ","N","D"],["zan","feb","mar","awi","mɛ","zuw","zul","uti","sɛt","ɔku","now","des"],["zanwuye","feburuye","marisi","awirili","mɛ","zuwɛn","zuluye","uti","sɛtanburu","ɔkutɔburu","nowanburu","desanburu"]],u,[["J.-C. ɲɛ","ni J.-C."],u,["jezu krisiti ɲɛ","jezu krisiti minkɛ"]],1,[6,0],["d/M/y","d MMM, y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"XOF","F CFA","sefa Fraŋ (BCEAO)",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    