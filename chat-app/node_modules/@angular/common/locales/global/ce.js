/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['ce'] = ["ce",[["AM","PM"],u,u],u,[["кӀи","ор","ши","кха","еа","пӀе","шуо"],u,["кӀира","оршот","шинара","кхаара","еара","пӀераска","шуот"],["кӀи","ор","ши","кха","еа","пӀе","шуо"]],[["кӀ","о","ш","кх","е","пӀ","ш"],["кӀи","ор","ши","кха","еа","пӀе","шуо"],["кӀира","оршот","шинара","кхаара","еара","пӀераска","шуот"],["кӀи","ор","ши","кха","еа","пӀе","шуо"]],[["Я","Ф","М","А","М","И","И","А","С","О","Н","Д"],["янв","фев","мар","апр","май","июн","июл","авг","сен","окт","ноя","дек"],["январь","февраль","март","апрель","май","июнь","июль","август","сентябрь","октябрь","ноябрь","декабрь"]],u,[["в. э. тӀ. я","в. э"],u,["Ӏийса пайхамар вина де кхачале","Ӏийса пайхамар вина дийнахь дуьйна"]],1,[6,0],["y-MM-dd","y MMM d","y MMMM d","y MMMM d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","Терхьаш дац",":"],["#,##0.###","#,##0 %","#,##0.00 ¤","#E0"],"RUB","₽","Российн сом",{"BYN":[u,"р."],"JPY":["JP¥","¥"],"PHP":[u,"₱"],"RON":[u,"лей"],"RUB":["₽"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    