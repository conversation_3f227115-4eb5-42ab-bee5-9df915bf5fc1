/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['ccp'] = ["ccp",[["AM","PM"],u,u],u,[["𑄢𑄧","𑄥𑄧","𑄟𑄧","𑄝𑄪","𑄝𑄳𑄢𑄨","𑄥𑄪","𑄥𑄧"],["𑄢𑄧𑄝𑄨","𑄥𑄧𑄟𑄴","𑄟𑄧𑄁𑄉𑄧𑄣𑄴","𑄝𑄪𑄖𑄴","𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴","𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴","𑄥𑄧𑄚𑄨"],["𑄢𑄧𑄝𑄨𑄝𑄢𑄴","𑄥𑄧𑄟𑄴𑄝𑄢𑄴","𑄟𑄧𑄁𑄉𑄧𑄣𑄴𑄝𑄢𑄴","𑄝𑄪𑄖𑄴𑄝𑄢𑄴","𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴𑄝𑄢𑄴","𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴𑄝𑄢𑄴","𑄥𑄧𑄚𑄨𑄝𑄢𑄴"],["𑄢𑄧𑄝𑄨","𑄥𑄧𑄟𑄴","𑄟𑄧𑄁𑄉𑄧𑄣𑄴","𑄝𑄪𑄖𑄴","𑄝𑄳𑄢𑄨𑄥𑄪𑄛𑄴","𑄥𑄪𑄇𑄴𑄇𑄮𑄢𑄴","𑄥𑄧𑄚𑄨"]],u,[["𑄎","𑄜𑄬","𑄟","𑄃𑄬","𑄟𑄬","𑄎𑄪𑄚𑄴","𑄎𑄪","𑄃","𑄥𑄬","𑄃𑄧","𑄚𑄧","𑄓𑄨"],["𑄎𑄚𑄪","𑄜𑄬𑄛𑄴","𑄟𑄢𑄴𑄌𑄧","𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴","𑄟𑄬","𑄎𑄪𑄚𑄴","𑄎𑄪𑄣𑄭","𑄃𑄉𑄧𑄌𑄴𑄑𑄴","𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴","𑄃𑄧𑄇𑄴𑄑𑄮𑄝𑄧𑄢𑄴","𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴","𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄢𑄴"],["𑄎𑄚𑄪𑄠𑄢𑄨","𑄜𑄬𑄛𑄴𑄝𑄳𑄢𑄪𑄠𑄢𑄨","𑄟𑄢𑄴𑄌𑄧","𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴","𑄟𑄬","𑄎𑄪𑄚𑄴","𑄎𑄪𑄣𑄭","𑄃𑄉𑄧𑄌𑄴𑄑𑄴","𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴","𑄃𑄧𑄇𑄴𑄑𑄬𑄝𑄧𑄢𑄴","𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴","𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄧𑄢𑄴"]],[["𑄎","𑄜𑄬","𑄟","𑄃𑄬","𑄟𑄬","𑄎𑄪𑄚𑄴","𑄎𑄪","𑄃","𑄥𑄬","𑄃𑄧","𑄚𑄧","𑄓𑄨"],["𑄎𑄚𑄪𑄠𑄢𑄨","𑄜𑄬𑄛𑄴𑄝𑄳𑄢𑄪𑄠𑄢𑄨","𑄟𑄢𑄴𑄌𑄧","𑄃𑄬𑄛𑄳𑄢𑄨𑄣𑄴","𑄟𑄬","𑄎𑄪𑄚𑄴","𑄎𑄪𑄣𑄭","𑄃𑄉𑄧𑄌𑄴𑄑𑄴","𑄥𑄬𑄛𑄴𑄑𑄬𑄟𑄴𑄝𑄧𑄢𑄴","𑄃𑄧𑄇𑄴𑄑𑄮𑄝𑄧𑄢𑄴","𑄚𑄧𑄞𑄬𑄟𑄴𑄝𑄧𑄢𑄴","𑄓𑄨𑄥𑄬𑄟𑄴𑄝𑄧𑄢𑄴"],u],[["𑄈𑄳𑄢𑄨𑄌𑄴𑄑𑄴𑄛𑄫𑄢𑄴𑄝𑄧","𑄈𑄳𑄢𑄨𑄌𑄴𑄑𑄛𑄴𑄘𑄧"],u,u],0,[6,0],["d/M/yy","d MMM, y","d MMMM, y","EEEE, d MMMM, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##,##0.###","#,##,##0%","#,##,##0.00¤","#E0"],"BDT","৳","𑄝𑄁𑄣𑄘𑄬𑄥𑄨 𑄑𑄬𑄋",{"BDT":["৳"],"BYN":[u,"р."],"JPY":["JP¥","¥"],"PHP":[u,"₱"],"STD":[u,"Db"],"THB":["฿"],"TWD":["NT$"],"USD":["US$","$"]},"ltr", plural, [[["𑄛𑄧𑄖𑄳𑄠𑄃𑄟𑄧𑄣𑄳𑄠𑄬","𑄝𑄬𑄚𑄳𑄠𑄬","𑄘𑄨𑄝𑄪𑄎𑄳𑄠","𑄝𑄬𑄣𑄳𑄠𑄬","𑄥𑄎𑄧𑄚𑄳𑄠","𑄢𑄬𑄖𑄴"],u,u],u,[["04:00","06:00"],["06:00","12:00"],["12:00","16:00"],["16:00","18:00"],["18:00","20:00"],["20:00","04:00"]]]];
  })(globalThis);
    