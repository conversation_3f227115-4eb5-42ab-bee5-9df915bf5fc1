/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["su-Latn", [["AM", "PM"], u, u], u, [["M", "S", "S", "R", "K", "J", "S"], ["Mng", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>m", "Sap"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saptu"], ["Mng", "<PERSON>", "<PERSON>", "Reb", "Ke<PERSON>", "Ju<PERSON>", "Sap"]], u, [["J", "P", "M", "A", "M", "J", "<PERSON>", "A", "<PERSON>", "O", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>é<PERSON>", "<PERSON>", "<PERSON>", "Ags", "<PERSON>é<PERSON>", "Okt", "<PERSON>p", "<PERSON><PERSON>"], ["<PERSON>uari", "<PERSON><PERSON>bruari", "<PERSON>t", "April", "<PERSON>éi", "Juni", "Juli", "Agustus", "Séptémber", "Oktober", "<PERSON>pémber", "D<PERSON>émber"]], u, [["<PERSON>", "M"], u, u], 0, [6, 0], ["d/M/yy", "d M<PERSON> y", "d M<PERSON>M y", "EEEE, d MMMM y"], ["H.mm", "H.mm.ss", "H.mm.ss z", "H.mm.ss zzzz"], ["{1}, {0}", u, "{1} 'jam' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", "."], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "IDR", "Rp", "Rupee Indonésia", { "IDR": ["Rp"] }, "ltr", plural];
//# sourceMappingURL=su-Latn.js.map