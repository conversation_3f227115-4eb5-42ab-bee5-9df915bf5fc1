{"version": 3, "file": "te.js", "sourceRoot": "", "sources": ["te.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,OAAO,EAAC,KAAK,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,WAAW,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,QAAQ,EAAC,cAAc,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"te\",[[\"ఉ\",\"సా\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"ఆ\",\"సో\",\"మ\",\"బు\",\"గు\",\"శు\",\"శ\"],[\"ఆది\",\"సోమ\",\"మంగళ\",\"బుధ\",\"గురు\",\"శుక్ర\",\"శని\"],[\"ఆదివారం\",\"సోమవారం\",\"మంగళవారం\",\"బుధవారం\",\"గురువారం\",\"శుక్రవారం\",\"శనివారం\"],[\"ఆది\",\"సోమ\",\"మం\",\"బుధ\",\"గురు\",\"శుక్ర\",\"శని\"]],u,[[\"జ\",\"ఫి\",\"మా\",\"ఏ\",\"మే\",\"జూ\",\"జు\",\"ఆ\",\"సె\",\"అ\",\"న\",\"డి\"],[\"జన\",\"ఫిబ్ర\",\"మార్చి\",\"ఏప్రి\",\"మే\",\"జూన్\",\"జులై\",\"ఆగ\",\"సెప్టెం\",\"అక్టో\",\"నవం\",\"డిసెం\"],[\"జనవరి\",\"ఫిబ్రవరి\",\"మార్చి\",\"ఏప్రిల్\",\"మే\",\"జూన్\",\"జులై\",\"ఆగస్టు\",\"సెప్టెంబర్\",\"అక్టోబర్\",\"నవంబర్\",\"డిసెంబర్\"]],u,[[\"క్రీపూ\",\"క్రీశ\"],u,[\"క్రీస్తు పూర్వం\",\"క్రీస్తు శకం\"]],0,[0,0],[\"dd-MM-yy\",\"d MMM, y\",\"d MMMM, y\",\"d, MMMM y, EEEE\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,\"{1} {0}కి\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##0%\",\"¤#,##,##0.00\",\"#E0\"],\"INR\",\"₹\",\"భారతదేశ రూపాయి\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}