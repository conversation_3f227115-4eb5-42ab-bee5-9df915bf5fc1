{"version": 3, "file": "mai.js", "sourceRoot": "", "sources": ["mai.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,YAAY,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,OAAO,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"mai\",[[\"AM\",\"PM\"],u,[\"भोर\",\"सांझ\"]],[[\"AM\",\"PM\"],u,u],[[\"र\",\"सो\",\"मं\",\"बु\",\"गु\",\"शु\",\"श\"],[\"रवि\",\"सोम\",\"मंगल\",\"बुध\",\"गुरु\",\"शुक्र\",\"शनि\"],[\"रवि दिन\",\"सोम दिन\",\"मंगल दिन\",\"बुध दिन\",\"बृहस्पति दिन\",\"शुक्र दिन\",\"शनि दिन\"],[\"रवि\",\"सोम\",\"मंगल\",\"बुध\",\"गुरु\",\"शुक्र\",\"शनि\"]],u,[[\"ज\",\"फ\",\"मा\",\"अ\",\"म\",\"जू\",\"जु\",\"अ\",\"सि\",\"अ\",\"न\",\"दि\"],[\"जन॰\",\"फ़र॰\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुल॰\",\"अग॰\",\"सित॰\",\"अक्तू॰\",\"नव॰\",\"दिस॰\"],[\"जनवरी\",\"फरवरी\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुलाई\",\"अगस्त\",\"सितंबर\",\"अक्तूबर\",\"नवंबर\",\"दिसंबर\"]],[[\"ज\",\"फ\",\"मा\",\"अ\",\"म\",\"जू\",\"जु\",\"अ\",\"सि\",\"अ\",\"न\",\"दि\"],[\"जन॰\",\"फर॰\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुल॰\",\"अग॰\",\"सित॰\",\"अक्तू॰\",\"नव॰\",\"दिस॰\"],[\"जनवरी\",\"फरवरी\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुलाई\",\"अगस्त\",\"सितंबर\",\"अक्टूबर\",\"नवंबर\",\"दिसंबर\"]],[[\"ईसा-पूर्व\",\"ईसवी\"],u,u],0,[0,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} के {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"[#E0]\"],\"INR\",\"₹\",\"भारतीय रुपया\",{\"JPY\":[\"JP¥\",\"¥\"]},\"ltr\", plural];\n"]}