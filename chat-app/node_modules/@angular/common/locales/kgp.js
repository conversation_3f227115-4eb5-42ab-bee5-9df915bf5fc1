/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["kgp", [["AM", "PM"], u, u], u, [["N.", "P.", "R.", "T.", "V.", "P.", "S."], ["num.", "pir.", "rég.", "tẽg.", "vẽn.", "pén.", "sav."], ["numĩggu", "pir-kurã-há", "régre-kurã-há", "tẽgtũ-kurã-há", "vẽnhkãgra-kurã-há", "pénkar-kurã-há", "savnu"], ["N.", "1kh.", "2kh.", "3kh.", "4kh.", "5kh.", "S."]], u, [["1K", "2K", "3K", "4K", "5K", "6K", "7K", "8K", "9K", "10K", "11K", "12K"], ["1Ky.", "2Ky.", "3Ky.", "4Ky.", "5Ky.", "6Ky.", "7Ky.", "8Ky.", "9Ky.", "10Ky.", "11Ky.", "12Ky."], ["1-Kysã", "2-Kysã", "3-Kysã", "4-Kysã", "5-Kysã", "6-Kysã", "7-Kysã", "8-Kysã", "9-Kysã", "10-Kysã", "11-Kysã", "12-Kysã"]], u, [["C.j.", "C.kk."], u, ["Cristo jo", "Cristo kar kỹ"]], 0, [6, 0], ["dd/MM/y", "d 'ne' MMM, y", "d 'ne' MMMM, y", "EEEE, d 'ne' MMMM, y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "BRL", "R$", "Mrasir Rejar", { "AUD": ["AU$", "$"], "BYN": [u, "p."], "FJD": ["FJC", "$"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "PTE": ["Vẽj."], "RON": [u, "L"], "SYP": [u, "S£"], "THB": ["฿"], "TWD": ["NT$"], "USD": ["US$", "$"], "XOF": ["CFA"], "ZMK": ["SMK"] }, "ltr", plural];
//# sourceMappingURL=kgp.js.map