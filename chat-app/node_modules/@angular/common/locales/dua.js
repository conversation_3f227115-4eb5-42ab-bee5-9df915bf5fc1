/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["dua", [["idiɓa", "ebyámu"], u, u], u, [["e", "m", "k", "m", "ŋ", "ɗ", "e"], ["ét", "mɔ́s", "kwa", "muk", "ŋgi", "ɗón", "esa"], ["éti", "mɔ́sú", "kwasú", "mukɔ́sú", "ŋgisú", "ɗónɛsú", "esaɓasú"], ["ét", "mɔ́s", "kwa", "muk", "ŋgi", "ɗón", "esa"]], u, [["d", "ŋ", "s", "d", "e", "e", "m", "d", "n", "m", "t", "e"], ["di", "ŋgɔn", "sɔŋ", "diɓ", "emi", "esɔ", "mad", "diŋ", "nyɛt", "may", "tin", "elá"], ["dimɔ́di", "ŋgɔndɛ", "sɔŋɛ", "diɓáɓá", "emiasele", "esɔpɛsɔpɛ", "madiɓɛ́díɓɛ́", "diŋgindi", "nyɛtɛki", "mayésɛ́", "tiníní", "eláŋgɛ́"]], u, [["ɓ.Ys", "mb.Ys"], u, ["ɓoso ɓwá yáɓe lá", "mbúsa kwédi a Yés"]], 1, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "XAF", "FCFA", "XAF", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=dua.js.map