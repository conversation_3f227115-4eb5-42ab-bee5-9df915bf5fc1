/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 0)
        return 0;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ksh", [["v.M.", "n.M."], u, ["<PERSON>r vörmid<PERSON>ach<PERSON>", "Uhr nommendaachs"]], [["v.M.", "n.M."], u, ["V<PERSON>rmeddaach", "<PERSON>mmendaach"]], [["S", "M", "D", "M", "D", "F", "S"], ["Su.", "Mo.", "Di.", "Me.", "Du.", "Fr.", "Sa."], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]], u, [["J", "F", "<PERSON>", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON><PERSON>b", "<PERSON><PERSON>z", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>uj", "<PERSON><PERSON>p", "<PERSON>t", "<PERSON>", "<PERSON>z"], ["<PERSON><PERSON>wa", "<PERSON><PERSON><PERSON>wa", "<PERSON><PERSON><PERSON>z", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Juuli", "Oujoß", "Septämber", "Oktohber", "Novämber", "Dezämber"]], [["J", "F", "M", "A", "M", "J", "J", "O", "S", "O", "N", "D"], ["Jan.", "Fäb.", "Mäz.", "Apr.", "Mai", "Jun.", "Jul.", "Ouj.", "Säp.", "Okt.", "Nov.", "Dez."], ["Jannewa", "Fäbrowa", "Määz", "Aprell", "Mai", "Juuni", "Juuli", "Oujoß", "Septämber", "Oktohber", "Novämber", "Dezämber"]], [["vC", "nC"], ["v. Chr.", "n. Chr."], ["vür Krestos", "noh Krestos"]], 1, [6, 0], ["d. M. y", "d. MMM. y", "d. MMMM y", "EEEE, 'dä' d. MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "−", "×10^", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "EUR", "€", "Euro", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=ksh.js.map