/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ksb", [["makeo", "nyiaghuo"], u, u], u, [["2", "3", "4", "5", "A", "I", "1"], ["Jpi", "Jtt", "Jmn", "Jtn", "<PERSON>h", "<PERSON><PERSON>", "<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>ju<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["J<PERSON>", "J<PERSON>", "Jmn", "Jtn", "<PERSON>h", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "J", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>go", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>"], ["<PERSON>uali", "<PERSON>luali", "<PERSON><PERSON>", "<PERSON>plili", "<PERSON>", "<PERSON>i", "<PERSON><PERSON>", "A<PERSON>ti", "<PERSON>emba", "<PERSON>toba", "<PERSON>emba", "<PERSON>emba"]], u, [["<PERSON><PERSON>", "<PERSON><PERSON>"], u, ["<PERSON>bla ya <PERSON>listo", "<PERSON>ada ya Klisto"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "TZS", "TSh", "shilingi ya Tanzania", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=ksb.js.map