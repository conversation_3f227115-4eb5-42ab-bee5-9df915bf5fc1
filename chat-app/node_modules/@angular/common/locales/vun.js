/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["vun", [["utuko", "kyiukonyi"], u, u], u, [["<PERSON>", "<PERSON>", "<PERSON>", "J", "A", "I", "J"], ["Jpi", "Jtt", "Jnn", "Jtn", "<PERSON>h", "<PERSON><PERSON>", "<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], ["J<PERSON>", "J<PERSON>", "Jnn", "Jtn", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "J", "A", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>"], ["<PERSON>ua<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>yi", "<PERSON>", "<PERSON>yi", "July<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>em<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>em<PERSON>", "<PERSON>em<PERSON>"]], u, [["<PERSON><PERSON>", "<PERSON><PERSON>"], u, ["<PERSON><PERSON> ya <PERSON>tu", "<PERSON><PERSON> ya <PERSON>tu"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "TZS", "TSh", "Shilingi ya Tanzania", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=vun.js.map