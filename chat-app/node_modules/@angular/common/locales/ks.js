/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ks", [["AM", "PM"], u, u], u, [["ا", "ژ", "ب", "ب", "ب", "ج", "ب"], ["آتھوار", "ژٔندٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"], ["اَتھوار", "ژٔندرٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"], ["آتھوار", "ژٔندٕروار", "بۆموار", "بودوار", "برؠسوار", "جُمہ", "بٹوار"]], u, [["ج", "ف", "م", "ا", "م", "ج", "ج", "ا", "س", "س", "ا", "ن"], ["جنؤری", "فرؤری", "مارٕچ", "اپریل", "مئی", "جوٗن", "جوٗلایی", "اگست", "ستمبر", "اکتوٗبر", "نومبر", "دسمبر"], u], u, [["بی سی", "اے ڈی"], u, ["قبٕل مسیٖح", "عیٖسوی سنہٕ"]], 0, [0, 0], ["M/d/yy", "MMM d, y", "MMMM d, y", "EEEE, MMMM d, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{0} پٮ۪ٹھۍ {1}", u], [".", "،", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "INR", "₹", "ہِندُستٲنۍ رۄپَے", {}, "rtl", plural];
//# sourceMappingURL=ks.js.map