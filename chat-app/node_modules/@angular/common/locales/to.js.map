{"version": 3, "file": "to.js", "sourceRoot": "", "sources": ["to.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,YAAY,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,cAAc,EAAC,SAAS,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,UAAU,EAAC,IAAI,EAAC,MAAM,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,SAAS,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,mBAAmB,EAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"to\",[[\"AM\",\"PM\"],u,[\"heng<PERSON><PERSON>i\",\"efia<PERSON>\"]],[[\"AM\",\"PM\"],u,[\"HH\",\"EA\"]],[[\"S\",\"M\",\"T\",\"P\",\"T\",\"F\",\"T\"],[\"Sāp\",\"<PERSON>ōn\",\"Tūs\",\"<PERSON><PERSON>\",\"<PERSON>ʻa\",\"Fal\",\"Tok\"],[\"<PERSON>ā<PERSON>\",\"<PERSON>ōnite\",\"Tūsite\",\"<PERSON>ulelulu\",\"<PERSON>ʻapulelulu\",\"<PERSON>alaite\",\"<PERSON><PERSON><PERSON>\"],[\"<PERSON>āp\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>ʻ<PERSON>\",\"<PERSON>al\",\"Tok\"]],u,[[\"S\",\"F\",\"M\",\"E\",\"M\",\"S\",\"S\",\"A\",\"S\",\"O\",\"N\",\"T\"],[\"Sān\",\"Fēp\",\"Maʻa\",\"ʻEpe\",\"Mē\",\"Sun\",\"Siu\",\"ʻAok\",\"Sep\",\"ʻOka\",\"Nōv\",\"Tīs\"],[\"Sānuali\",\"Fēpueli\",\"Maʻasi\",\"ʻEpeleli\",\"Mē\",\"Sune\",\"Siulai\",\"ʻAokosi\",\"Sepitema\",\"ʻOkatopa\",\"Nōvema\",\"Tīsema\"]],u,[[\"KM\",\"TS\"],u,[\"ki muʻa\",\"taʻu ʻo Sīsū\"]],1,[6,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",\"{1}, {0}\",u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"TF\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"TOP\",\"T$\",\"Paʻanga fakatonga\",{\"AUD\":[\"AUD$\",\"AU$\"],\"FJD\":[u,\"F$\"],\"JPY\":[\"JP¥\",\"¥\"],\"NZD\":[\"NZD$\",\"NZ$\"],\"SBD\":[u,\"S$\"],\"TOP\":[\"T$\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}