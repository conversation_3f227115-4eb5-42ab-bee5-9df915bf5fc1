{"version": 3, "file": "ksf.js", "sourceRoot": "", "sources": ["ksf.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,gBAAgB,EAAC,eAAe,EAAC,cAAc,EAAC,cAAc,EAAC,eAAe,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,eAAe,EAAC,uBAAuB,EAAC,uBAAuB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"ksf\",[[\"sárúwá\",\"cɛɛ́nko\"],u,u],u,[[\"s\",\"l\",\"m\",\"m\",\"j\",\"j\",\"s\"],[\"sɔ́n\",\"lǝn\",\"maa\",\"mɛk\",\"jǝǝ\",\"júm\",\"sam\"],[\"sɔ́ndǝ\",\"lǝndí\",\"maadí\",\"mɛkrɛdí\",\"jǝǝdí\",\"júmbá\",\"samdí\"],[\"sɔ́n\",\"lǝn\",\"maa\",\"mɛk\",\"jǝǝ\",\"júm\",\"sam\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"ŋ1\",\"ŋ2\",\"ŋ3\",\"ŋ4\",\"ŋ5\",\"ŋ6\",\"ŋ7\",\"ŋ8\",\"ŋ9\",\"ŋ10\",\"ŋ11\",\"ŋ12\"],[\"ŋwíí a ntɔ́ntɔ\",\"ŋwíí akǝ bɛ́ɛ\",\"ŋwíí akǝ ráá\",\"ŋwíí akǝ nin\",\"ŋwíí akǝ táan\",\"ŋwíí akǝ táafɔk\",\"ŋwíí akǝ táabɛɛ\",\"ŋwíí akǝ táaraa\",\"ŋwíí akǝ táanin\",\"ŋwíí akǝ ntɛk\",\"ŋwíí akǝ ntɛk di bɔ́k\",\"ŋwíí akǝ ntɛk di bɛ́ɛ\"]],u,[[\"d.Y.\",\"k.Y.\"],u,[\"di Yɛ́sus aká yálɛ\",\"cámɛɛn kǝ kǝbɔpka Y\"]],1,[6,0],[\"d/M/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"XAF\",\"FCFA\",\"fráŋ\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}