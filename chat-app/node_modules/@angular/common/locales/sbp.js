/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["sbp", [["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], u, u], u, [["M", "J", "J", "J", "A", "<PERSON>", "J"], ["Mul", "Jtt", "Jnn", "Jtn", "<PERSON>h", "<PERSON><PERSON>", "<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ju<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>ju<PERSON><PERSON>", "Ju<PERSON><PERSON><PERSON>"], ["Mul", "Jtt", "Jnn", "Jtn", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Mup", "<PERSON><PERSON>", "<PERSON>h", "<PERSON>n", "Mag", "<PERSON>j", "<PERSON><PERSON>", "<PERSON>pg", "<PERSON>e", "<PERSON>k", "<PERSON>s", "<PERSON>h"], ["Mupalangulwa", "<PERSON>wi<PERSON>e", "<PERSON><PERSON>nde", "<PERSON>nyi", "<PERSON><PERSON>nde <PERSON>gali", "<PERSON><PERSON>mbi", "<PERSON>ship<PERSON><PERSON>", "<PERSON>pu<PERSON><PERSON>", "<PERSON>ny<PERSON>", "<PERSON>khu", "<PERSON><PERSON><PERSON><PERSON>mbwe", "<PERSON>ha<PERSON>"]], u, [["<PERSON>", "<PERSON><PERSON>"], u, ["Ashanali uKilisito", "Pamwandi ya Kilisto"]], 1, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00¤", "#E0"], "TZS", "TSh", "Ihela ya Tansaniya", { "JPY": ["JP¥", "¥"], "TZS": ["TSh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=sbp.js.map