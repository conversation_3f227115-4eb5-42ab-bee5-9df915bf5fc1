/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["mer", [["RŨ", "ŨG"], u, u], u, [["K", "M", "W", "W", "W", "W", "J"], ["KIU", "MRA", "WAI", "WET", "WEN", "WTN", "JUM"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>uk<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>hat<PERSON>", "Wen<PERSON>", "Wetano", "Jumamosi"], ["KIU", "MRA", "WAI", "WET", "WEN", "WTN", "J<PERSON>"]], u, [["J", "F", "M", "Ĩ", "M", "N", "N", "A", "S", "O", "N", "D"], ["JAN", "FEB", "MAC", "ĨPU", "MĨĨ", "NJU", "NJR", "AGA", "SPT", "OKT", "<PERSON>V", "DEC"], ["<PERSON>uarĩ", "Feburuarĩ", "<PERSON>hi", "Ĩpurũ", "<PERSON>ĩĩ", "Njuni", "Njuraĩ", "Agasti", "<PERSON>emba", "<PERSON>tũba", "Novemba", "<PERSON><PERSON>mba"]], u, [["M<PERSON>", "N<PERSON>"], u, ["Mbere ya <PERSON>tũ", "Nyuma ya <PERSON>tũ"]], 0, [6, 0], ["dd/MM/y", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "KES", "Ksh", "Shilingi ya Kenya", { "JPY": ["JP¥", "¥"], "KES": ["Ksh"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=mer.js.map