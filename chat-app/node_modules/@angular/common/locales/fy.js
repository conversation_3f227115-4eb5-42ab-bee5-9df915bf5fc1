/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;
    if (i === 1 && v === 0)
        return 1;
    return 5;
}
export default ["fy", [["AM", "PM"], u, u], u, [["S", "M", "T", "W", "T", "F", "S"], ["si", "mo", "ti", "wo", "to", "fr", "so"], ["snein", "moandei", "tiisdei", "woansdei", "tongersdei", "freed", "sneon"], ["si", "mo", "ti", "wo", "to", "fr", "so"]], u, [["J", "F", "M", "A", "M", "J", "J", "A", "S", "O", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>t", "<PERSON>", "<PERSON>"], ["<PERSON>ne<PERSON>s", "<PERSON>rew<PERSON>", "<PERSON><PERSON>", "April", "<PERSON><PERSON>e", "<PERSON>y", "July", "<PERSON>", "<PERSON>imber", "<PERSON>tober", "<PERSON>mber", "<PERSON>im<PERSON>"]], u, [["f.K.", "n.K."], ["f.Kr.", "n.Kr."], ["Foar Kristus", "nei Kristus"]], 1, [6, 0], ["dd-MM-yy", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, "{1} 'om' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00;¤ #,##0.00-", "#E0"], "EUR", "€", "Euro", { "AUD": ["AU$", "$"], "CAD": ["C$", "$"], "FJD": ["FJ$", "$"], "JPY": ["JP¥", "¥"], "SBD": ["SI$", "$"], "THB": ["฿"], "USD": ["US$", "$"], "XPF": [] }, "ltr", plural];
//# sourceMappingURL=fy.js.map