/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["dyo", [["AM", "PM"], u, u], u, [["D", "T", "T", "A", "A", "A", "S"], ["Dim", "Ten", "Tal", "Ala", "Ara", "Arj", "Sib"], ["<PERSON><PERSON>", "<PERSON>e<PERSON>", "<PERSON><PERSON>a", "Alar<PERSON>", "Ara<PERSON><PERSON>", "<PERSON>r<PERSON><PERSON>", "<PERSON>biti"], ["Dim", "Ten", "Tal", "Ala", "Ara", "Arj", "Sib"]], u, [["S", "F", "M", "A", "M", "S", "<PERSON>", "U", "<PERSON>", "O", "<PERSON>", "<PERSON>"], ["Sa", "<PERSON>", "<PERSON>", "Ab", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Ut", "Se", "Ok", "No", "De"], ["<PERSON>vie", "Fébirie", "<PERSON>", "Aburil", "Mee", "Sueŋ", "Súuyee", "Ut", "Settembar", "Oktobar", "Novembar", "Disambar"]], u, [["ArY", "AtY"], u, ["Ariŋuu Yeesu", "Atooŋe Yeesu"]], 1, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "XOF", "F CFA", "seefa yati BCEAO", { "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=dyo.js.map