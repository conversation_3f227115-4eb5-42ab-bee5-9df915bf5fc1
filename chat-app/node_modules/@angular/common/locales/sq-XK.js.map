{"version": 3, "file": "sq-XK.js", "sourceRoot": "", "sources": ["sq-XK.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,OAAO,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,QAAQ,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,eAAe,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,QAAQ,EAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,IAAI,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"sq-XK\",[[\"p.d.\",\"m.d.\"],u,[\"e paradites\",\"e pasdites\"]],[[\"p.d.\",\"m.d.\"],u,[\"paradite\",\"pasdite\"]],[[\"d\",\"h\",\"m\",\"m\",\"e\",\"p\",\"sh\"],[\"Die\",\"Hën\",\"Mar\",\"Mër\",\"Enj\",\"Pre\",\"Sht\"],[\"e diel\",\"e hënë\",\"e martë\",\"e mërkurë\",\"e enjte\",\"e premte\",\"e shtunë\"],[\"die\",\"hën\",\"mar\",\"mër\",\"enj\",\"pre\",\"sht\"]],[[\"d\",\"h\",\"m\",\"m\",\"e\",\"p\",\"sh\"],[\"die\",\"hën\",\"mar\",\"mër\",\"enj\",\"pre\",\"sht\"],[\"e diel\",\"e hënë\",\"e martë\",\"e mërkurë\",\"e enjte\",\"e premte\",\"e shtunë\"],[\"die\",\"hën\",\"mar\",\"mër\",\"enj\",\"pre\",\"sht\"]],[[\"j\",\"sh\",\"m\",\"p\",\"m\",\"q\",\"k\",\"g\",\"sh\",\"t\",\"n\",\"dh\"],[\"jan\",\"shk\",\"mar\",\"pri\",\"maj\",\"qer\",\"korr\",\"gush\",\"sht\",\"tet\",\"nën\",\"dhj\"],[\"janar\",\"shkurt\",\"mars\",\"prill\",\"maj\",\"qershor\",\"korrik\",\"gusht\",\"shtator\",\"tetor\",\"nëntor\",\"dhjetor\"]],u,[[\"p.K.\",\"mb.K.\"],u,[\"para Krishtit\",\"mbas Krishtit\"]],1,[6,0],[\"d.M.yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,\"{1} 'në' {0}\",u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"Euroja\",{\"AFN\":[],\"ALL\":[\"Lekë\"],\"AMD\":[],\"AOA\":[],\"ARS\":[],\"AUD\":[\"A$\",\"AUD\"],\"AZN\":[],\"BAM\":[],\"BBD\":[],\"BDT\":[],\"BMD\":[],\"BND\":[],\"BOB\":[],\"BRL\":[],\"BSD\":[],\"BWP\":[],\"BZD\":[],\"CAD\":[\"CA$\",\"CAD\"],\"CLP\":[],\"CNY\":[\"CN¥\",\"CNY\"],\"COP\":[],\"CRC\":[],\"CUC\":[],\"CUP\":[],\"CZK\":[],\"DKK\":[],\"DOP\":[],\"EGP\":[],\"FJD\":[],\"FKP\":[],\"GBP\":[\"£\",\"GBP\"],\"GEL\":[],\"GIP\":[],\"GNF\":[],\"GTQ\":[],\"GYD\":[],\"HKD\":[\"HK$\",\"HKS\"],\"HNL\":[],\"HRK\":[],\"HUF\":[],\"IDR\":[],\"ILS\":[\"₪\",\"ILS\"],\"INR\":[\"₹\",\"INR\"],\"ISK\":[],\"JMD\":[],\"JPY\":[\"JP¥\",\"JPY\"],\"KHR\":[],\"KMF\":[],\"KPW\":[],\"KRW\":[\"₩\",\"KRW\"],\"KYD\":[],\"KZT\":[],\"LAK\":[],\"LBP\":[],\"LKR\":[],\"LRD\":[],\"MGA\":[],\"MMK\":[],\"MNT\":[],\"MUR\":[],\"MXN\":[\"MX$\",\"MXN\"],\"MYR\":[],\"NAD\":[],\"NGN\":[],\"NIO\":[],\"NOK\":[],\"NPR\":[],\"NZD\":[\"NZ$\",\"NZD\"],\"PHP\":[],\"PKR\":[],\"PLN\":[],\"PYG\":[],\"RON\":[],\"RUB\":[],\"RWF\":[],\"SBD\":[],\"SEK\":[],\"SGD\":[],\"SHP\":[],\"SRD\":[],\"SSP\":[],\"STN\":[],\"SYP\":[],\"THB\":[\"฿\",\"THB\"],\"TOP\":[],\"TRY\":[],\"TTD\":[],\"TWD\":[\"NT$\",\"TWD\"],\"UAH\":[],\"USD\":[\"US$\",\"USD\"],\"UYU\":[],\"VND\":[\"₫\",\"VND\"],\"XCD\":[\"EC$\",\"XCD\"],\"ZAR\":[],\"ZMW\":[]},\"ltr\", plural];\n"]}