import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, FormsModule],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App {
  protected title = 'chat-app';
  protected profileName = 'John Doe';
  protected messageText = '';

  selectFile() {
    const fileInput = document.getElementById('file-input') as HTMLInputElement;
    fileInput?.click();
  }

  toggleVoice() {
    console.log('Fonction vocale activée');
    // Ici vous pouvez implémenter la reconnaissance vocale
  }

  sendMessage() {
    if (this.messageText.trim()) {
      console.log('Message envoyé:', this.messageText);
      // Ici vous pouvez implémenter l'envoi du message
      this.messageText = '';
    }
  }
}
