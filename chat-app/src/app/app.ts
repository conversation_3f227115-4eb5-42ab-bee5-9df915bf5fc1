import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, FormsModule, CommonModule],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App {
  protected title = 'chat-app';
  protected profileName = 'John Doe';
  protected messageText = '';
  protected selectedFile: File | null = null;
  protected selectedFileName = '';

  selectFile() {
    const fileInput = document.getElementById('file-input') as HTMLInputElement;
    fileInput?.click();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.selectedFileName = this.selectedFile.name;
      console.log('Fichier sélectionné:', this.selectedFile.name);
      console.log('Taille du fichier:', this.selectedFile.size, 'bytes');
      console.log('Type du fichier:', this.selectedFile.type);
    }
  }

  removeSelectedFile() {
    this.selectedFile = null;
    this.selectedFileName = '';
    const fileInput = document.getElementById('file-input') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  toggleVoice() {
    console.log('Fonction vocale activée');
    // Ici vous pouvez implémenter la reconnaissance vocale
  }

  sendMessage() {
    if (this.messageText.trim() || this.selectedFile) {
      console.log('Message envoyé:', this.messageText);
      if (this.selectedFile) {
        console.log('Fichier joint:', this.selectedFile.name);
      }
      // Ici vous pouvez implémenter l'envoi du message avec le fichier
      this.messageText = '';
      this.removeSelectedFile();
    }
  }
}
