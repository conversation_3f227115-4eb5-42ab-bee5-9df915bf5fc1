import { Component, AfterViewChecked, ElementRef, ViewChild } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  file?: {
    name: string;
    size: number;
    type: string;
  };
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  lastActivity: Date;
}

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, FormsModule, CommonModule],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements AfterViewChecked {
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  
  protected title = 'chat-app';
  protected profileName = 'John Doe';
  protected messageText = '';
  protected selectedFile: File | null = null;
  protected selectedFileName = '';
  protected isVoiceRecording = false;
  protected isTyping = false;
  private shouldScrollToBottom = false;
  
  // Gestion des conversations
  protected conversations: Conversation[] = [];
  protected currentConversation: Conversation | null = null;
  protected showSidebar = true;
  protected activeTab: 'files' | 'history' | 'settings' = 'history';

  constructor() {
    this.initializeDefaultConversation();
    this.loadConversationsFromStorage();
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  initializeDefaultConversation() {
    const defaultConversation: Conversation = {
      id: 'default',
      title: 'Nouvelle conversation',
      messages: [],
      lastActivity: new Date()
    };
    this.conversations = [defaultConversation];
    this.currentConversation = defaultConversation;
  }

  loadConversationsFromStorage() {
    const saved = localStorage.getItem('chat-conversations');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        this.conversations = parsed.map((conv: any) => ({
          ...conv,
          lastActivity: new Date(conv.lastActivity),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }))
        }));
        if (this.conversations.length > 0) {
          this.currentConversation = this.conversations[0];
        }
      } catch (error) {
        console.error('Erreur lors du chargement des conversations:', error);
      }
    }
  }

  saveConversationsToStorage() {
    localStorage.setItem('chat-conversations', JSON.stringify(this.conversations));
  }

  selectFile() {
    const fileInput = document.getElementById('file-input') as HTMLInputElement;
    fileInput?.click();
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.selectedFileName = this.selectedFile.name;
    }
  }

  removeSelectedFile() {
    this.selectedFile = null;
    this.selectedFileName = '';
    const fileInput = document.getElementById('file-input') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  }

  toggleVoice() {
    this.isVoiceRecording = !this.isVoiceRecording;
    if (this.isVoiceRecording) {
      console.log('Enregistrement vocal démarré...');
      setTimeout(() => {
        this.isVoiceRecording = false;
        this.messageText = "Message vocal transcrit automatiquement";
      }, 3000);
    } else {
      console.log('Enregistrement vocal arrêté');
    }
  }

  sendMessage() {
    if (this.messageText.trim() || this.selectedFile) {
      const message: Message = {
        id: Date.now().toString(),
        text: this.messageText.trim(),
        isUser: true,
        timestamp: new Date(),
        file: this.selectedFile ? {
          name: this.selectedFile.name,
          size: this.selectedFile.size,
          type: this.selectedFile.type
        } : undefined
      };

      if (this.currentConversation) {
        this.currentConversation.messages.push(message);
        this.currentConversation.lastActivity = new Date();
        
        if (this.currentConversation.messages.length === 1) {
          this.currentConversation.title = this.messageText.substring(0, 30) + '...';
        }
      }

      this.messageText = '';
      this.removeSelectedFile();
      this.saveConversationsToStorage();
      this.shouldScrollToBottom = true;
      this.simulateResponse();
    }
  }

  simulateResponse() {
    this.isTyping = true;
    
    setTimeout(() => {
      const responses = [
        "C'est une excellente question ! Laissez-moi vous aider avec cela.",
        "Je comprends votre demande. Voici ce que je peux vous proposer...",
        "Merci pour votre message. Je vais analyser cela pour vous.",
        "Intéressant ! Voici mon point de vue sur cette question.",
        "Je suis là pour vous aider. Pouvez-vous me donner plus de détails ?",
        "Parfait ! Je vais traiter votre demande immédiatement.",
        "C'est un sujet fascinant. Permettez-moi de vous expliquer...",
        "Excellente observation ! Voici ce que je pense à ce sujet."
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      
      const responseMessage: Message = {
        id: Date.now().toString(),
        text: randomResponse,
        isUser: false,
        timestamp: new Date()
      };

      if (this.currentConversation) {
        this.currentConversation.messages.push(responseMessage);
        this.currentConversation.lastActivity = new Date();
      }

      this.isTyping = false;
      this.saveConversationsToStorage();
      this.shouldScrollToBottom = true;
    }, 1500);
  }

  private scrollToBottom(): void {
    try {
      if (this.messagesContainer) {
        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Erreur lors du défilement:', err);
    }
  }

  setActiveTab(tab: 'files' | 'history' | 'settings') {
    this.activeTab = tab;
  }

  createNewConversation() {
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title: 'Nouvelle conversation',
      messages: [],
      lastActivity: new Date()
    };
    this.conversations.unshift(newConversation);
    this.currentConversation = newConversation;
    this.saveConversationsToStorage();
  }

  selectConversation(conversation: Conversation) {
    this.currentConversation = conversation;
    this.shouldScrollToBottom = true;
  }

  deleteConversation(conversation: Conversation) {
    const index = this.conversations.indexOf(conversation);
    if (index > -1) {
      this.conversations.splice(index, 1);
      if (this.currentConversation === conversation) {
        this.currentConversation = this.conversations.length > 0 ? this.conversations[0] : null;
        if (!this.currentConversation) {
          this.initializeDefaultConversation();
        }
      }
      this.saveConversationsToStorage();
    }
  }

  toggleSidebar() {
    this.showSidebar = !this.showSidebar;
  }

  initializeDiscussion() {
    // Vérifier s'il y a des messages à effacer
    const hasMessages = this.currentConversation?.messages && this.currentConversation.messages.length > 0;

    if (hasMessages) {
      // Confirmer avec l'utilisateur avant de réinitialiser
      const confirmReset = confirm('Êtes-vous sûr de vouloir initialiser une nouvelle discussion ? Cela effacera tous les messages de la conversation actuelle.');

      if (!confirmReset) {
        return; // L'utilisateur a annulé
      }
    }

    if (this.currentConversation) {
      // Vider les messages de la conversation actuelle
      this.currentConversation.messages = [];
      this.currentConversation.title = 'Nouvelle conversation';
      this.currentConversation.lastActivity = new Date();

      // Sauvegarder les changements
      this.saveConversationsToStorage();

      // Réinitialiser les états
      this.isTyping = false;
      this.messageText = '';
      this.removeSelectedFile();

      // Message de confirmation dans la console
      console.log('Discussion initialisée avec succès');

      // Optionnel : Ajouter un message de bienvenue automatique
      setTimeout(() => {
        if (this.currentConversation && this.currentConversation.messages.length === 0) {
          const welcomeMessage = {
            id: Date.now().toString(),
            text: "Bonjour ! Je suis votre assistant IA. Comment puis-je vous aider aujourd'hui ?",
            isUser: false,
            timestamp: new Date()
          };
          this.currentConversation.messages.push(welcomeMessage);
          this.saveConversationsToStorage();
          this.shouldScrollToBottom = true;
        }
      }, 500);
    }
  }

  formatTime(date: Date): string {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatDate(date: Date): string {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Aujourd\'hui';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Hier';
    } else {
      return date.toLocaleDateString('fr-FR');
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
