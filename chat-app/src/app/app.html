<style>
  @import "app.css";
</style>

<main class="main">
  <div class="content">
    <!-- crée un side bar a gauche contenant 3 boutons  -->
     <div class="sidebar">
      <button class="sidebar-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z"
          />
        </svg>
        <span>Contacts</span>
        </button>
      <button class="sidebar-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span>Groupes</span>
        </button>
      <button class="sidebar-button">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span>Paramètres</span>
        </button>
    </div>
    <!-- crée un main content a droite contenant un header et un chat area -->
    <div class="main-content">
      <div class="header">
        <div class="profile">
          <div class="profile-avatar">JD</div>
          <div class="profile-name">John Doe</div>
        </div>
      </div>
      <div class="chat-area">
        <div class="welcome-message">
          <h1>Bienvenue sur ChatApp</h1>
          <p>Commencez à discuter avec vos contacts</p>
        </div>
      </div>
      <div class="input-area">
        <input
          type="text"
          class="text-input"
          placeholder="Écrivez un message..."
        />
        <button class="send-button">Envoyer</button>
      </div>
    </div>
  </div>
</main>

<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->
<!-- * * * * * * * * * * * The content above * * * * * * * * * * * * -->
<!-- * * * * * * * * * * is only a placeholder * * * * * * * * * * * -->
<!-- * * * * * * * * * * and can be replaced.  * * * * * * * * * * * -->
<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->
<!-- * * * * * * * * * * End of Placeholder  * * * * * * * * * * * * -->
<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->


<router-outlet />
