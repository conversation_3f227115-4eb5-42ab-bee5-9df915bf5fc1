<style>
  @import "app.css";
</style>

<main class="main">
  <!-- Header global -->
  <div class="header">
    <div class="header-left">
      <button class="sidebar-toggle" (click)="toggleSidebar()" title="Basculer la sidebar">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
        </svg>
      </button>
      <h2 class="conversation-title" *ngIf="currentConversation">
        {{ currentConversation.title }}
      </h2>
    </div>
    <div class="profile">
      <div class="profile-avatar">{{ profileName.charAt(0).toUpperCase() }}</div>
      <div class="profile-name">{{ profileName }}</div>
    </div>
  </div>

  <div class="content">
    <!-- Overlay pour mobile -->
    <div class="sidebar-overlay" *ngIf="showSidebar" (click)="toggleSidebar()"></div>

    <!-- Sidebar gauche dynamique -->
    <div class="sidebar" [class.collapsed]="!showSidebar" *ngIf="showSidebar">
      <!-- Onglets de navigation -->
      <div class="sidebar-tabs">
        <button
          class="sidebar-tab"
          [class.active]="activeTab === 'history'"
          (click)="setActiveTab('history')"
          title="Historique des conversations"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Historique</span>
        </button>
        <button
          class="sidebar-tab"
          [class.active]="activeTab === 'files'"
          (click)="setActiveTab('files')"
          title="Gestion des fichiers"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
          </svg>
          <span>Fichiers</span>
        </button>
        <button
          class="sidebar-tab"
          [class.active]="activeTab === 'settings'"
          (click)="setActiveTab('settings')"
          title="Paramètres"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>Paramètres</span>
        </button>
      </div>

      <!-- Contenu dynamique selon l'onglet actif -->
      <div class="sidebar-content">
        <!-- Onglet Historique -->
        <div *ngIf="activeTab === 'history'" class="history-tab">
          <div class="tab-header">
            <h3>Conversations</h3>
            <button class="new-chat-btn" (click)="createNewConversation()" title="Nouvelle conversation">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
            </button>
          </div>
          <div class="conversations-list">
            <div
              *ngFor="let conversation of conversations"
              class="conversation-item"
              [class.active]="currentConversation?.id === conversation.id"
              (click)="selectConversation(conversation)"
            >
              <div class="conversation-info">
                <div class="conversation-title">{{ conversation.title }}</div>
                <div class="conversation-date">{{ formatDate(conversation.lastActivity) }}</div>
              </div>
              <button
                class="delete-conversation-btn"
                (click)="deleteConversation(conversation); $event.stopPropagation()"
                title="Supprimer la conversation"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Onglet Fichiers -->
        <div *ngIf="activeTab === 'files'" class="files-tab">
          <div class="tab-header">
            <h3>Fichiers récents</h3>
          </div>
          <div class="files-list">
            <div class="file-item" *ngFor="let conversation of conversations">
              <div *ngFor="let message of conversation.messages">
                <div *ngIf="message.file" class="file-entry">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="file-icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                  </svg>
                  <div class="file-details">
                    <div class="file-name">{{ message.file.name }}</div>
                    <div class="file-size">{{ formatFileSize(message.file.size) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Onglet Paramètres -->
        <div *ngIf="activeTab === 'settings'" class="settings-tab">
          <div class="tab-header">
            <h3>Paramètres</h3>
          </div>
          <div class="settings-list">
            <div class="setting-item">
              <label>Nom d'utilisateur</label>
              <input type="text" [(ngModel)]="profileName" class="setting-input">
            </div>
            <div class="setting-item">
              <label>Thème</label>
              <select class="setting-select">
                <option>Clair</option>
                <option>Sombre</option>
                <option>Automatique</option>
              </select>
            </div>
            <div class="setting-item">
              <button class="setting-button" (click)="toggleSidebar()">
                {{ showSidebar ? 'Masquer' : 'Afficher' }} la sidebar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- crée un main content a droite contenant un header et un chat area -->
    <div class="main-content">
      <div class="chat-area">
        <!-- Message de bienvenue si aucun message -->
        <div class="welcome-message" *ngIf="!currentConversation?.messages.length">
          <h1>Bienvenue, {{ profileName }} !</h1>
          <p>Comment puis-je vous aider aujourd'hui ?</p>
        </div>

        <!-- Liste des messages -->
        <div class="messages-container" #messagesContainer *ngIf="currentConversation?.messages.length">
          <div
            *ngFor="let message of currentConversation.messages"
            class="message"
            [class.user-message]="message.isUser"
            [class.assistant-message]="!message.isUser"
          >
            <div class="message-content">
              <div class="message-avatar">
                <span *ngIf="message.isUser">{{ profileName.charAt(0).toUpperCase() }}</span>
                <svg *ngIf="!message.isUser" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.847.813a4.5 4.5 0 00-3.09 3.09z" />
                </svg>
              </div>
              <div class="message-body">
                <div class="message-header">
                  <span class="message-author">{{ message.isUser ? profileName : 'Assistant' }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-text">{{ message.text }}</div>
                <div class="message-file" *ngIf="message.file">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="file-icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                  </svg>
                  <div class="file-info">
                    <div class="file-name">{{ message.file.name }}</div>
                    <div class="file-size">{{ formatFileSize(message.file.size) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Indicateur de frappe -->
          <div class="message assistant-message typing-indicator" *ngIf="isTyping">
            <div class="message-content">
              <div class="message-avatar">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.847.813a4.5 4.5 0 00-3.09 3.09z" />
                </svg>
              </div>
              <div class="message-body">
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="input-container">
        <!-- Affichage du fichier sélectionné -->
        <div class="selected-file" *ngIf="selectedFileName">
          <div class="file-info">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="file-icon"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
              />
            </svg>
            <span class="file-name">{{ selectedFileName }}</span>
            <button class="remove-file-btn" (click)="removeSelectedFile()">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        <div class="input-area">
          <!-- Bouton de choix de fichier -->
          <input
            type="file"
            id="file-input"
            class="file-input"
            (change)="onFileSelected($event)"
            accept="*/*"
          />
          <button class="input-button" (click)="selectFile()" title="Joindre un fichier">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M18.375 12.739l-7.693 7.693a4.5 4.5 0 01-6.364-6.364l10.94-10.94A3 3 0 1119.5 7.372L8.552 18.32m.009-.01l-.01.01m5.699-9.941l-7.81 7.81a1.5 1.5 0 002.112 2.13"
              />
            </svg>
          </button>

          <!-- Zone de texte -->
          <input
            type="text"
            class="text-input"
            placeholder="Écrivez votre message ici..."
            [(ngModel)]="messageText"
            (keyup.enter)="sendMessage()"
          />

          <!-- Bouton vocal -->
          <button
            class="input-button voice-button"
            [class.recording]="isVoiceRecording"
            (click)="toggleVoice()"
            title="Enregistrement vocal"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"
              />
            </svg>
            <div class="recording-indicator" *ngIf="isVoiceRecording">
              <span class="recording-dot"></span>
            </div>
          </button>

          <!-- Bouton d'envoi -->
          <button class="input-button send-button" (click)="sendMessage()" title="Envoyer le message">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->
<!-- * * * * * * * * * * * The content above * * * * * * * * * * * * -->
<!-- * * * * * * * * * * is only a placeholder * * * * * * * * * * * -->
<!-- * * * * * * * * * * and can be replaced.  * * * * * * * * * * * -->
<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->
<!-- * * * * * * * * * * End of Placeholder  * * * * * * * * * * * * -->
<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->


<router-outlet />
