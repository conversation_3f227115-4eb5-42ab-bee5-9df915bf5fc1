
  :host {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
      "Segoe UI Symbol";
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .chat-container {
    display: flex;
    height: 100vh;
    background-color: #f5f5f5;
  }

  /* Sidebar gauche */
  .sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: white;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .sidebar-button {
    background-color: #34495e;
    border: none;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.3s ease;
  }

  .sidebar-button:hover {
    background-color: #3498db;
  }

  .sidebar-button svg {
    width: 18px;
    height: 18px;
  }

  /* Zone principale */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* Header avec profil */
  .header {
    background-color: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .profile {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3498db;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
  }

  .profile-name {
    font-weight: 500;
    color: #2c3e50;
  }

  /* Zone de chat */
  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .welcome-message {
    text-align: center;
    margin-bottom: 40px;
  }

  .welcome-message h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
  }

  .welcome-message p {
    color: #7f8c8d;
    font-size: 1.1rem;
  }

  /* Zone d'input */
  .input-area {
    background-color: white;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 15px;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    border-radius: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }

  .text-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 20px;
    background-color: #f8f9fa;
  }

  .input-button {
    background-color: #3498db;
    border: none;
    color: white;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    width: 40px;
    height: 40px;
  }

  .input-button:hover {
    background-color: #2980b9;
  }

  .input-button svg {
    width: 20px;
    height: 20px;
  }

  .file-input {
    display: none;
  }

  @media screen and (max-width: 768px) {
    .sidebar {
      width: 200px;
    }

    .welcome-message h1 {
      font-size: 2rem;
    }

    .input-area {
      margin: 0 10px;
    }
  }
