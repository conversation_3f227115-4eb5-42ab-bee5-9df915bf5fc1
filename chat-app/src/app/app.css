
  :host {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
      "Segoe UI Symbol";
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .main {
    height: 100vh;
    background-color: #f7f7f8;
  }

  .content {
    display: flex;
    height: 100vh;
    background-color: #f7f7f8;
  }

  /* Sidebar gauche - Style ChatGPT */
  .sidebar {
    width: 260px;
    background-color: #171717;
    color: white;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-right: 1px solid #2d2d30;
  }

  .sidebar-button {
    background-color: transparent;
    border: none;
    color: #ececf1;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: background-color 0.2s ease;
    text-align: left;
    width: 100%;
  }

  .sidebar-button:hover {
    background-color: #2d2d30;
  }

  .sidebar-button svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  /* Zone principale - Style ChatGPT */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
  }

  /* Header avec profil - Style ChatGPT */
  .header {
    background-color: #ffffff;
    padding: 16px 24px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .profile:hover {
    background-color: #f5f5f5;
  }

  .profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
  }

  .profile-name {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
  }

  /* Zone de chat - Style ChatGPT */
  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    background-color: #ffffff;
  }

  .welcome-message {
    text-align: center;
    max-width: 600px;
  }

  .welcome-message h1 {
    color: #374151;
    font-size: 2rem;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.2;
  }

  .welcome-message p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.5;
  }

  /* Container pour l'input et le fichier sélectionné */
  .input-container {
    background-color: #ffffff;
    padding: 20px 24px 32px;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
  }

  /* Affichage du fichier sélectionné */
  .selected-file {
    margin-bottom: 12px;
    padding: 8px 12px;
    background-color: #f3f4f6;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .file-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
    flex-shrink: 0;
  }

  .file-name {
    flex: 1;
    font-size: 14px;
    color: #374151;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .remove-file-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .remove-file-btn:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  .remove-file-btn svg {
    width: 14px;
    height: 14px;
  }

  /* Zone d'input - Style ChatGPT */
  .input-area {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .text-input {
    flex: 1;
    border: 1px solid #d1d5db;
    outline: none;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 12px;
    background-color: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    resize: none;
    min-height: 24px;
    max-height: 200px;
  }

  .text-input:focus {
    border-color: #10a37f;
    box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
  }

  .text-input::placeholder {
    color: #9ca3af;
  }

  .input-button {
    background-color: #f3f4f6;
    border: none;
    color: #6b7280;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    flex-shrink: 0;
  }

  .input-button:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  .input-button.send-button {
    background-color: #10a37f;
    color: white;
  }

  .input-button.send-button:hover {
    background-color: #0d8f69;
  }

  .input-button svg {
    width: 18px;
    height: 18px;
  }

  .file-input {
    display: none;
  }

  /* Styles responsive */
  @media screen and (max-width: 768px) {
    .sidebar {
      width: 240px;
    }

    .welcome-message h1 {
      font-size: 1.75rem;
    }

    .input-container {
      padding: 16px 20px 24px;
      margin: 0 16px;
    }

    .header {
      padding: 12px 20px;
    }
  }

  @media screen and (max-width: 640px) {
    .content {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: auto;
      flex-direction: row;
      padding: 8px;
      border-right: none;
      border-bottom: 1px solid #2d2d30;
    }

    .sidebar-button {
      flex: 1;
      justify-content: center;
      padding: 8px;
    }

    .sidebar-button span {
      display: none;
    }

    .main-content {
      flex: 1;
    }
  }
