
  :host {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
      "Segoe UI Symbol";
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .main {
    min-height: 100vh;
    background-color: #f7f7f8;
    display: flex;
    flex-direction: column;
  }

  .content {
    display: flex;
    flex: 1;
    min-height: 100vh;
    background-color: #f7f7f8;
    transition: all 0.3s ease;
  }

  .content.sidebar-hidden {
    margin-left: 0;
  }

  /* Overlay pour mobile */
  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  /* Sidebar gauche - Style ChatGPT dynamique */
  .sidebar {
    width: 260px;
    min-width: 260px;
    max-width: 320px;
    background-color: #171717;
    color: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2d2d30;
    transition: all 0.3s ease;
    height: 100vh;
    position: relative;
    overflow: hidden;
  }

  .sidebar.collapsed {
    width: 60px;
    min-width: 60px;
  }

  /* Onglets de navigation */
  .sidebar-tabs {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    border-bottom: 1px solid #2d2d30;
  }

  .sidebar-tab {
    background-color: transparent;
    border: none;
    color: #ececf1;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
  }

  .sidebar-tab:hover {
    background-color: #2d2d30;
  }

  .sidebar-tab.active {
    background-color: #10a37f;
    color: white;
  }

  .sidebar-tab svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  /* Contenu de la sidebar */
  .sidebar-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
  }

  .tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .tab-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #ececf1;
  }

  .new-chat-btn {
    background-color: #2d2d30;
    border: none;
    color: #ececf1;
    padding: 6px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
  }

  .new-chat-btn:hover {
    background-color: #10a37f;
  }

  .new-chat-btn svg {
    width: 14px;
    height: 14px;
  }

  /* Liste des conversations */
  .conversations-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .conversation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: 1px solid transparent;
  }

  .conversation-item:hover {
    background-color: #2d2d30;
  }

  .conversation-item.active {
    background-color: #10a37f;
    border-color: #0d8f69;
  }

  .conversation-info {
    flex: 1;
    min-width: 0;
  }

  .conversation-title {
    font-size: 14px;
    font-weight: 500;
    color: #ececf1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 4px;
  }

  .conversation-date {
    font-size: 12px;
    color: #9ca3af;
  }

  .delete-conversation-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
  }

  .conversation-item:hover .delete-conversation-btn {
    opacity: 1;
  }

  .delete-conversation-btn:hover {
    background-color: #dc2626;
    color: white;
  }

  .delete-conversation-btn svg {
    width: 14px;
    height: 14px;
  }

  /* Onglet Fichiers */
  .files-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .file-entry {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background-color: #2d2d30;
    border-radius: 6px;
  }

  .file-entry .file-icon {
    width: 16px;
    height: 16px;
    color: #9ca3af;
    flex-shrink: 0;
  }

  .file-entry .file-details {
    flex: 1;
    min-width: 0;
  }

  .file-entry .file-name {
    font-size: 12px;
    color: #ececf1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .file-entry .file-size {
    font-size: 11px;
    color: #9ca3af;
  }

  /* Onglet Paramètres */
  .settings-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .setting-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .setting-item label {
    font-size: 14px;
    color: #ececf1;
    font-weight: 500;
  }

  .setting-input,
  .setting-select {
    background-color: #2d2d30;
    border: 1px solid #4b5563;
    color: #ececf1;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
  }

  .setting-input:focus,
  .setting-select:focus {
    outline: none;
    border-color: #10a37f;
  }

  .setting-button {
    background-color: #10a37f;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
  }

  .setting-button:hover {
    background-color: #0d8f69;
  }

  /* Zone principale - Style ChatGPT */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    min-width: 0;
    position: relative;
    height: 100vh;
    overflow: hidden;
  }

  /* Header avec profil - Style ChatGPT */
  .header {
    background-color: #ffffff;
    padding: 16px 24px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .sidebar-toggle {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .sidebar-toggle:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  .sidebar-toggle svg {
    width: 20px;
    height: 20px;
  }

  .conversation-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 300px;
  }

  .profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
  }

  .profile:hover {
    background-color: #f5f5f5;
  }

  .profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
  }

  .profile-name {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
  }

  /* Zone de chat - Style ChatGPT dynamique */
  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    overflow: hidden;
    position: relative;
    min-height: 0;
  }

  .welcome-message {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 40px 20px;
  }

  .welcome-message h1 {
    color: #374151;
    font-size: 2rem;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.2;
  }

  .welcome-message p {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.5;
  }

  /* Messages */
  .messages-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    scroll-behavior: smooth;
  }

  .messages-container::-webkit-scrollbar {
    width: 6px;
  }

  .messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  .message {
    display: flex;
    width: 100%;
    animation: messageSlideIn 0.3s ease-out;
  }

  .message.user-message {
    justify-content: flex-end;
  }

  .message.assistant-message {
    justify-content: flex-start;
  }

  .message-content {
    display: flex;
    gap: 12px;
    max-width: 70%;
    align-items: flex-start;
  }

  .user-message .message-content {
    flex-direction: row-reverse;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
  }

  .user-message .message-avatar {
    background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%);
    color: white;
  }

  .assistant-message .message-avatar {
    background-color: #f3f4f6;
    color: #6b7280;
  }

  .assistant-message .message-avatar svg {
    width: 16px;
    height: 16px;
  }

  .message-body {
    flex: 1;
    min-width: 0;
  }

  .message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }

  .message-author {
    font-weight: 600;
    font-size: 14px;
    color: #374151;
  }

  .message-time {
    font-size: 12px;
    color: #9ca3af;
  }

  .message-text {
    background-color: #f3f4f6;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
    word-wrap: break-word;
  }

  .user-message .message-text {
    background-color: #10a37f;
    color: white;
  }

  .message-file {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #e5e7eb;
    border-radius: 8px;
    border: 1px solid #d1d5db;
  }

  .user-message .message-file {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .message-file .file-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
    flex-shrink: 0;
  }

  .user-message .message-file .file-icon {
    color: rgba(255, 255, 255, 0.8);
  }

  .message-file .file-info {
    flex: 1;
    min-width: 0;
  }

  .message-file .file-name {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .user-message .message-file .file-name {
    color: white;
  }

  .message-file .file-size {
    font-size: 11px;
    color: #6b7280;
  }

  .user-message .message-file .file-size {
    color: rgba(255, 255, 255, 0.7);
  }

  /* Indicateur de frappe */
  .typing-indicator {
    opacity: 0.8;
  }

  .typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    background-color: #f3f4f6;
    border-radius: 12px;
  }

  .typing-dots span {
    width: 6px;
    height: 6px;
    background-color: #9ca3af;
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
  }

  .typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }

  .typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }

  .typing-dots span:nth-child(3) {
    animation-delay: 0s;
  }

  /* Container pour l'input et le fichier sélectionné */
  .input-container {
    background-color: #ffffff;
    padding: 20px 24px 32px;
    max-width: 768px;
    margin: 0 auto;
    width: 100%;
  }

  /* Affichage du fichier sélectionné */
  .selected-file {
    margin-bottom: 12px;
    padding: 8px 12px;
    background-color: #f3f4f6;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .file-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
    flex-shrink: 0;
  }

  .file-name {
    flex: 1;
    font-size: 14px;
    color: #374151;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .remove-file-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .remove-file-btn:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  .remove-file-btn svg {
    width: 14px;
    height: 14px;
  }

  /* Zone d'input - Style ChatGPT */
  .input-area {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .text-input {
    flex: 1;
    border: 1px solid #d1d5db;
    outline: none;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 12px;
    background-color: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    resize: none;
    min-height: 24px;
    max-height: 200px;
  }

  .text-input:focus {
    border-color: #10a37f;
    box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1);
  }

  .text-input::placeholder {
    color: #9ca3af;
  }

  .input-button {
    background-color: #f3f4f6;
    border: none;
    color: #6b7280;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    flex-shrink: 0;
  }

  .input-button:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  .input-button.send-button {
    background-color: #10a37f;
    color: white;
  }

  .input-button.send-button:hover {
    background-color: #0d8f69;
  }

  .input-button svg {
    width: 18px;
    height: 18px;
  }

  /* Bouton vocal avec animation */
  .voice-button {
    position: relative;
  }

  .voice-button.recording {
    background-color: #dc2626 !important;
    animation: voicePulse 1s infinite;
  }

  .recording-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 12px;
    height: 12px;
    background-color: #dc2626;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .recording-dot {
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
    animation: recordingBlink 1s infinite;
  }

  .file-input {
    display: none;
  }

  /* Animations */
  @keyframes messageSlideIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes typingDots {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes voicePulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes recordingBlink {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.3;
    }
  }

  /* Styles responsive */
  @media screen and (max-width: 768px) {
    .sidebar {
      width: 240px;
    }

    .welcome-message h1 {
      font-size: 1.75rem;
    }

    .input-container {
      padding: 16px 20px 24px;
      margin: 0 16px;
    }

    .header {
      padding: 12px 20px;
    }

    .message-content {
      max-width: 85%;
    }

    .messages-container {
      padding: 16px;
    }
  }

  @media screen and (max-width: 640px) {
    .sidebar-overlay {
      display: block;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      width: 280px;
      min-width: 280px;
    }

    .sidebar:not(.collapsed) {
      transform: translateX(0);
    }

    .content {
      width: 100%;
    }

    .main-content {
      width: 100%;
      flex: 1;
    }

    .message-content {
      max-width: 95%;
    }

    .input-container {
      padding: 12px 16px 20px;
      margin: 0 8px;
    }

    .conversation-title {
      font-size: 13px;
      max-width: 200px;
    }

    .message-text {
      font-size: 13px;
      padding: 10px 14px;
    }

    .header-left .conversation-title {
      display: none;
    }
  }

  @media screen and (max-width: 480px) {
    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
    }

    .sidebar.show {
      transform: translateX(0);
    }

    .main-content {
      width: 100%;
    }

    .header {
      padding: 8px 16px;
    }

    .profile-name {
      display: none;
    }

    .input-area {
      gap: 6px;
    }

    .input-button {
      width: 32px;
      height: 32px;
    }

    .input-button svg {
      width: 16px;
      height: 16px;
    }
  }
